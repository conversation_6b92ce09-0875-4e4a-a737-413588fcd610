use anyhow::Result;
use std::env;
use std::time::Duration;
use tokio::time::sleep;
use solana_vntr_sniper::core::app_state::EnhancedAppState;
use solana_vntr_sniper::common::config::Config;
use anchor_client::solana_sdk::signature::Keypair;

/// Demonstration of the enhanced app state with rate limiting, circuit breakers, and retry logic
#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt::init();
    
    println!("🚀 Enhanced App State Demo");
    println!("This demo shows the new concurrency improvements:");
    println!("- Rate limiting for RPC calls, swaps, and price checks");
    println!("- Circuit breakers for fault tolerance");
    println!("- Lock-free global state management");
    println!("- Automatic retry logic with exponential backoff");
    println!("- Connection pooling for RPC clients");
    println!();

    // Load environment variables
    dotenv::dotenv().ok();
    
    // Get RPC endpoint
    let rpc_endpoint = env::var("RPC_HTTP")
        .unwrap_or_else(|_| "https://api.devnet.solana.com".to_string());
    
    // Create a test keypair (in production, load from file)
    let keypair = Keypair::new();
    
    println!("📡 Creating enhanced app state with RPC endpoint: {}", rpc_endpoint);
    
    // Create enhanced app state
    let app_state = EnhancedAppState::from_env(keypair, rpc_endpoint).await?;
    
    println!("✅ Enhanced app state created successfully!");
    println!();

    // Demonstrate health check
    println!("🏥 Performing initial health check...");
    let health = app_state.health_check().await;
    println!("Health status: {}", health.status_string());
    println!();

    // Demonstrate system status
    println!("📊 System status:");
    let status = app_state.get_system_status();
    println!("  Global state: {}", status.global_stats.status_string());
    println!("  Rate limiter: Available permits - RPC: {}, Swaps: {}, Price checks: {}", 
        status.rate_limiter_status.rpc_available,
        status.rate_limiter_status.swap_available,
        status.rate_limiter_status.price_check_available
    );
    println!("  Circuit breakers:");
    println!("    RPC: {}", status.rpc_circuit_breaker_stats.status_string());
    println!("    Swap: {}", status.swap_circuit_breaker_stats.status_string());
    println!("    Price: {}", status.price_circuit_breaker_stats.status_string());
    println!("  RPC pool: {}/{} connections available", 
        status.rpc_pool_status.available, 
        status.rpc_pool_status.max_size
    );
    println!();

    // Demonstrate protected RPC operations
    println!("🔗 Testing protected RPC operations...");
    
    // Test multiple concurrent RPC calls to show rate limiting
    let mut handles = Vec::new();
    for i in 0..5 {
        let app_state = app_state.clone();
        let handle = tokio::spawn(async move {
            let result = app_state.execute_rpc_protected(|client| async move {
                println!("  RPC call {} starting...", i + 1);
                let blockhash = client.get_latest_blockhash().await
                    .map_err(|e| anyhow::anyhow!("RPC error: {}", e))?;
                println!("  RPC call {} completed: {}", i + 1, blockhash);
                Ok::<_, anyhow::Error>(blockhash)
            }).await;
            
            match result {
                Ok(_) => println!("  ✅ RPC call {} succeeded", i + 1),
                Err(e) => println!("  ❌ RPC call {} failed: {}", i + 1, e),
            }
        });
        handles.push(handle);
    }
    
    // Wait for all RPC calls to complete
    for handle in handles {
        handle.await?;
    }
    println!();

    // Demonstrate global state management
    println!("🌐 Testing lock-free global state...");
    
    // Add some test token tracking
    let test_mint = "So11111111111111111111111111111111111111112"; // SOL mint
    let mut token_info = solana_vntr_sniper::core::lock_free_globals::TokenTrackingInfo::new();
    token_info.update_price(100.0);
    
    app_state.globals.update_token_tracking(test_mint.to_string(), token_info.clone());
    println!("  Added token tracking for: {}", test_mint);
    
    // Retrieve and update
    if let Some(mut info) = app_state.globals.get_token_tracking(test_mint) {
        info.update_price(105.0);
        app_state.globals.update_token_tracking(test_mint.to_string(), info);
        println!("  Updated token price to: 105.0");
    }
    
    // Test buying enabled/disabled
    println!("  Current buying enabled: {}", app_state.globals.is_buying_enabled());
    app_state.globals.set_buying_enabled(false);
    println!("  Disabled buying");
    println!("  Buying enabled now: {}", app_state.globals.is_buying_enabled());
    app_state.globals.set_buying_enabled(true);
    println!("  Re-enabled buying");
    println!();

    // Demonstrate circuit breaker behavior
    println!("🔌 Testing circuit breaker behavior...");
    
    // Force a circuit breaker to open for demonstration
    app_state.price_circuit_breaker.force_open();
    println!("  Forced price circuit breaker to OPEN state");
    
    // Try a price check operation (should fail fast)
    let result = app_state.execute_price_check_protected(|| async {
        Ok::<f64, anyhow::Error>(100.0)
    }).await;
    
    match result {
        Ok(_) => println!("  ❌ Price check unexpectedly succeeded"),
        Err(e) => println!("  ✅ Price check failed fast as expected: {}", e),
    }
    
    // Reset circuit breaker
    app_state.price_circuit_breaker.force_close();
    println!("  Reset price circuit breaker to CLOSED state");
    
    // Try again (should succeed)
    let result = app_state.execute_price_check_protected(|| async {
        Ok::<f64, anyhow::Error>(100.0)
    }).await;
    
    match result {
        Ok(price) => println!("  ✅ Price check succeeded after reset: {}", price),
        Err(e) => println!("  ❌ Price check failed: {}", e),
    }
    println!();

    // Demonstrate retry logic
    println!("🔄 Testing retry logic...");
    
    let mut attempt_count = 0;
    let result = app_state.execute_rpc_protected(|_client| async move {
        attempt_count += 1;
        println!("  Retry attempt: {}", attempt_count);
        
        if attempt_count < 3 {
            // Simulate a retryable error
            Err(anyhow::anyhow!("network error"))
        } else {
            // Succeed on third attempt
            Ok("Success after retries!")
        }
    }).await;
    
    match result {
        Ok(msg) => println!("  ✅ Operation succeeded: {}", msg),
        Err(e) => println!("  ❌ Operation failed: {}", e),
    }
    println!();

    // Monitor system for a short period
    println!("📈 Monitoring system for 10 seconds...");
    for i in 1..=5 {
        sleep(Duration::from_secs(2)).await;
        
        let health = app_state.health_check().await;
        let status = app_state.get_system_status();
        
        println!("  Check {}: {} | Tracked tokens: {} | Active positions: {}", 
            i,
            if health.healthy { "✅ Healthy" } else { "❌ Issues" },
            status.global_stats.tracked_tokens,
            status.global_stats.bought_tokens
        );
    }
    println!();

    // Demonstrate graceful shutdown
    println!("🛑 Performing graceful shutdown...");
    app_state.shutdown().await?;
    println!("✅ Shutdown completed successfully");
    
    println!();
    println!("🎉 Enhanced App State Demo completed!");
    println!("The new system provides:");
    println!("  - 🚦 Rate limiting to prevent resource exhaustion");
    println!("  - 🔌 Circuit breakers for fault tolerance");
    println!("  - 🔄 Automatic retry logic for transient failures");
    println!("  - 🌐 Lock-free global state for better performance");
    println!("  - 🏥 Comprehensive health monitoring");
    println!("  - 📊 Detailed metrics and observability");

    Ok(())
}

/// Helper function to demonstrate migration from legacy app state
#[allow(dead_code)]
async fn demonstrate_migration() -> Result<()> {
    // Get legacy config
    let config = Config::new().await;
    let legacy_app_state = &config.lock().await.app_state;
    
    // Create enhanced app state from legacy
    let rpc_endpoint = env::var("RPC_HTTP")
        .unwrap_or_else(|_| "https://api.devnet.solana.com".to_string());
    
    let enhanced_app_state = EnhancedAppState::from_legacy(legacy_app_state, rpc_endpoint).await?;
    
    println!("✅ Successfully migrated from legacy app state to enhanced app state");
    
    // Now you can use all the enhanced features
    let health = enhanced_app_state.health_check().await;
    println!("Enhanced app state health: {}", health.status_string());
    
    Ok(())
}
