//! Priority Queue System Demo
//! 
//! This example demonstrates how to use the priority queue system for arbitrage opportunities.
//! It shows opportunity detection, priority scoring, and execution ordering.

use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;
use solana_sdk::pubkey::Pubkey;
use uuid::Uuid;

use solana_vntr_sniper::core::priority_queue::{
    OpportunityQueue, PriorityQueueConfig, ArbitrageOpportunity, ArbitrageStrategy,
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Priority Queue System Demo");
    println!("=====================================");

    // Create a priority queue with custom configuration
    let config = PriorityQueueConfig {
        profit_weight: 0.4,           // 40% weight on profit
        gas_efficiency_weight: 0.3,   // 30% weight on gas efficiency
        success_probability_weight: 0.2, // 20% weight on success probability
        liquidity_weight: 0.1,        // 10% weight on liquidity
        time_decay_weight: 0.0,       // No time decay for demo
        max_queue_size: 10,
        opportunity_ttl_seconds: 60,
        min_profit_threshold_bps: 50, // 0.5% minimum profit
        max_gas_cost_sol: 0.01,       // Maximum 0.01 SOL gas cost
        min_liquidity_threshold: 100_000_000, // Minimum 0.1 SOL liquidity
    };

    let queue = Arc::new(OpportunityQueue::with_config(config));

    // Demo 1: Basic opportunity addition and retrieval
    println!("\n📊 Demo 1: Basic Operations");
    demo_basic_operations(&queue).await?;

    // Demo 2: Priority ordering based on different factors
    println!("\n🎯 Demo 2: Priority Ordering");
    demo_priority_ordering(&queue).await?;

    // Demo 3: Performance metrics tracking
    println!("\n📈 Demo 3: Performance Metrics");
    demo_performance_metrics(&queue).await?;

    // Demo 4: Real-time monitoring
    println!("\n⏱️  Demo 4: Real-time Monitoring");
    demo_real_time_monitoring(&queue).await?;

    println!("\n✅ Demo completed successfully!");
    Ok(())
}

async fn demo_basic_operations(queue: &Arc<OpportunityQueue>) -> Result<(), Box<dyn std::error::Error>> {
    println!("Adding sample arbitrage opportunities...");

    // Create different types of opportunities
    let opportunities = vec![
        create_sample_opportunity(
            "High Profit Multi-DEX",
            ArbitrageStrategy::MultiDex,
            5.2, // 5.2% profit
            180_000, // gas cost
            2_000_000_000, // 2 SOL liquidity
            1, // route complexity
        ),
        create_sample_opportunity(
            "Medium Profit Two-Hop",
            ArbitrageStrategy::TwoHop { intermediate_token: Pubkey::new_unique() },
            3.1, // 3.1% profit
            350_000, // higher gas cost
            1_500_000_000, // 1.5 SOL liquidity
            2, // route complexity
        ),
        create_sample_opportunity(
            "Low Profit High Liquidity",
            ArbitrageStrategy::MultiDex,
            1.8, // 1.8% profit
            120_000, // low gas cost
            10_000_000_000, // 10 SOL liquidity
            1, // route complexity
        ),
    ];

    // Add opportunities to queue
    for (i, opportunity) in opportunities.iter().enumerate() {
        queue.add_opportunity(opportunity.clone()).await?;
        println!("  ✓ Added opportunity {}: {}", i + 1, opportunity.id);
    }

    println!("Queue size: {}", queue.size().await);

    // Peek at the highest priority opportunity
    if let Some(top_opportunity) = queue.peek_next_opportunity().await {
        println!("🥇 Top priority opportunity:");
        print_opportunity_details(&top_opportunity);
    }

    // Clear queue for next demo
    queue.clear().await;
    Ok(())
}

async fn demo_priority_ordering(queue: &Arc<OpportunityQueue>) -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing priority ordering with different scoring factors...");

    // Add opportunities with varying characteristics
    let opportunities = vec![
        ("High Profit, High Gas", 8.0, 800_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1),
        ("Medium Profit, Low Gas", 4.0, 100_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1),
        ("Low Profit, Very Low Gas", 2.0, 50_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1),
        ("High Profit, Complex Route", 6.0, 500_000, 1_000_000_000, 
         ArbitrageStrategy::TwoHop { intermediate_token: Pubkey::new_unique() }, 2),
    ];

    for (name, profit, gas, liquidity, strategy, complexity) in opportunities {
        let opportunity = create_sample_opportunity(name, strategy, profit, gas, liquidity, complexity);
        queue.add_opportunity(opportunity).await?;
        println!("  ✓ Added: {} ({}% profit, {} gas)", name, profit, gas);
    }

    println!("\n🏆 Execution order based on composite scoring:");
    let mut rank = 1;
    while let Some(opportunity) = queue.get_next_opportunity().await {
        println!("  {}. Score: {:.4} | Profit: {:.1}% | Gas: {} | Strategy: {:?}",
                 rank,
                 opportunity.composite_score,
                 opportunity.opportunity.estimated_profit_pct,
                 opportunity.opportunity.estimated_gas_cost,
                 opportunity.opportunity.strategy_type);
        rank += 1;
    }

    Ok(())
}

async fn demo_performance_metrics(queue: &Arc<OpportunityQueue>) -> Result<(), Box<dyn std::error::Error>> {
    println!("Simulating trade executions and tracking performance...");

    // Create and execute some opportunities
    let opportunities = vec![
        create_sample_opportunity("Trade 1", ArbitrageStrategy::MultiDex, 3.0, 150_000, 1_000_000_000, 1),
        create_sample_opportunity("Trade 2", ArbitrageStrategy::MultiDex, 2.5, 180_000, 800_000_000, 1),
        create_sample_opportunity("Trade 3", ArbitrageStrategy::TwoHop { intermediate_token: Pubkey::new_unique() }, 4.0, 300_000, 1_200_000_000, 2),
    ];

    for (i, opportunity) in opportunities.iter().enumerate() {
        // Simulate execution results
        let success = i != 1; // Make second trade fail for demo
        let actual_profit = if success { opportunity.estimated_profit_sol * 0.95 } else { 0.0 }; // 5% slippage
        let actual_gas = opportunity.estimated_gas_cost + 10_000; // Slightly higher gas

        queue.update_performance_metrics(
            opportunity,
            success,
            actual_profit,
            actual_gas,
            Duration::from_millis(500 + i as u64 * 100),
        ).await?;

        println!("  ✓ Executed trade {}: {} (Profit: {:.4} SOL)", 
                 i + 1, 
                 if success { "SUCCESS" } else { "FAILED" },
                 actual_profit);
    }

    // Display performance metrics
    let metrics = queue.get_performance_metrics().await;
    println!("\n📊 Performance Summary:");
    println!("  Total trades: {}", metrics.total_trades);
    println!("  Successful trades: {}", metrics.successful_trades);
    println!("  Success rate: {:.1}%", 
             (metrics.successful_trades as f64 / metrics.total_trades as f64) * 100.0);
    println!("  Total profit: {:.4} SOL", metrics.total_profit_sol);

    // Display DEX success rates
    println!("\n🏪 DEX Performance:");
    for (dex_pair, success_rate) in &metrics.dex_success_rates {
        println!("  {}: {:.1}%", dex_pair, success_rate * 100.0);
    }

    Ok(())
}

async fn demo_real_time_monitoring(queue: &Arc<OpportunityQueue>) -> Result<(), Box<dyn std::error::Error>> {
    println!("Simulating real-time opportunity detection and monitoring...");

    // Spawn a task to continuously add opportunities
    let queue_clone = Arc::clone(queue);
    let producer_task = tokio::spawn(async move {
        for i in 0..5 {
            let opportunity = create_sample_opportunity(
                &format!("Real-time Opportunity {}", i + 1),
                ArbitrageStrategy::MultiDex,
                2.0 + (i as f64 * 0.5), // Increasing profit
                150_000 - (i * 10_000), // Decreasing gas cost
                1_000_000_000 + (i as u64 * 500_000_000), // Increasing liquidity
                1,
            );
            
            queue_clone.add_opportunity(opportunity).await.unwrap();
            println!("  📥 Added opportunity {} to queue", i + 1);
            sleep(Duration::from_millis(200)).await;
        }
    });

    // Monitor queue statistics
    let queue_clone = Arc::clone(queue);
    let monitor_task = tokio::spawn(async move {
        for _ in 0..10 {
            let stats = queue_clone.get_queue_stats().await;
            println!("  📊 Queue size: {} | Avg score: {:.4}", 
                     stats.total_opportunities,
                     stats.average_composite_score);
            sleep(Duration::from_millis(100)).await;
        }
    });

    // Execute opportunities as they become available
    let execution_task = tokio::spawn(async move {
        let mut executed = 0;
        while executed < 5 {
            if let Some(opportunity) = queue.get_next_opportunity().await {
                println!("  ⚡ Executing: {} (Score: {:.4})", 
                         opportunity.opportunity.id,
                         opportunity.composite_score);
                executed += 1;
                sleep(Duration::from_millis(150)).await;
            } else {
                sleep(Duration::from_millis(50)).await;
            }
        }
    });

    // Wait for all tasks to complete
    tokio::try_join!(producer_task, monitor_task, execution_task)?;

    println!("  ✅ Real-time monitoring demo completed");
    Ok(())
}

fn create_sample_opportunity(
    name: &str,
    strategy: ArbitrageStrategy,
    profit_pct: f64,
    gas_cost: u64,
    liquidity: u64,
    route_complexity: u8,
) -> ArbitrageOpportunity {
    let now = std::time::Instant::now();
    ArbitrageOpportunity {
        id: format!("{}-{}", name, Uuid::new_v4().to_string()[..8].to_string()),
        token_mint: Pubkey::new_unique(),
        strategy_type: strategy,
        buy_dex: "raydium".to_string(),
        sell_dex: "orca".to_string(),
        buy_price: 1.0,
        sell_price: 1.0 + (profit_pct / 100.0),
        price_difference_pct: profit_pct,
        estimated_profit_sol: profit_pct / 100.0 * 0.1, // Assuming 0.1 SOL trade
        estimated_profit_pct: profit_pct,
        liquidity_available: liquidity,
        detected_at: now,
        expires_at: now + Duration::from_secs(60),
        trade_amount: 100_000_000, // 0.1 SOL
        estimated_gas_cost: gas_cost,
        route_complexity,
    }
}

fn print_opportunity_details(prioritized_opportunity: &solana_vntr_sniper::core::priority_queue::PrioritizedOpportunity) {
    let opp = &prioritized_opportunity.opportunity;
    println!("    ID: {}", opp.id);
    println!("    Strategy: {:?}", opp.strategy_type);
    println!("    Profit: {:.2}% ({:.4} SOL)", opp.estimated_profit_pct, opp.estimated_profit_sol);
    println!("    Gas Cost: {} compute units", opp.estimated_gas_cost);
    println!("    Liquidity: {:.2} SOL", opp.liquidity_available as f64 / 1_000_000_000.0);
    println!("    Route: {} -> {}", opp.buy_dex, opp.sell_dex);
    println!("    Composite Score: {:.4}", prioritized_opportunity.composite_score);
    println!("    Individual Scores:");
    println!("      Profit: {:.4}", prioritized_opportunity.profit_score);
    println!("      Gas Efficiency: {:.4}", prioritized_opportunity.gas_efficiency_score);
    println!("      Success Probability: {:.4}", prioritized_opportunity.success_probability_score);
    println!("      Liquidity: {:.4}", prioritized_opportunity.liquidity_score);
    println!("      Time Decay: {:.4}", prioritized_opportunity.time_decay_score);
}
