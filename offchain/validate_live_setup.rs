#!/usr/bin/env cargo +nightly -Zscript
//! Live Trading Setup Validation Script
//! 
//! This script validates that the bot is properly configured for live trading
//! and checks all critical components before starting real market execution.

use anyhow::{Result, anyhow};
use dotenv::dotenv;
use std::env;
use colored::Colorize;

#[tokio::main]
async fn main() -> Result<()> {
    println!("{}", "🔍 VALIDATING LIVE TRADING SETUP".green().bold());
    println!("{}", "═".repeat(60).green());
    
    // Load environment variables
    dotenv().ok();
    
    let mut validation_passed = true;
    
    // 1. Check critical environment variables
    validation_passed &= validate_env_vars().await?;
    
    // 2. Check API connectivity
    validation_passed &= validate_api_connectivity().await?;
    
    // 3. Check wallet setup
    validation_passed &= validate_wallet_setup().await?;
    
    // 4. Check trading configuration
    validation_passed &= validate_trading_config().await?;
    
    // 5. Check safety parameters
    validation_passed &= validate_safety_config().await?;
    
    println!("\n{}", "═".repeat(60).green());
    
    if validation_passed {
        println!("{}", "✅ ALL VALIDATIONS PASSED - READY FOR LIVE TRADING".green().bold());
        println!("{}", "🚀 You can now start live trading with: cargo run --release -- live-trade".cyan());
    } else {
        println!("{}", "❌ VALIDATION FAILED - FIX ISSUES BEFORE LIVE TRADING".red().bold());
        println!("{}", "⚠️  Do not start live trading until all issues are resolved".yellow());
        std::process::exit(1);
    }
    
    Ok(())
}

async fn validate_env_vars() -> Result<bool> {
    println!("\n📋 Validating Environment Variables...");
    let mut passed = true;
    
    // Critical variables
    let critical_vars = [
        ("PRIVATE_KEY", "Trading wallet private key"),
        ("RPC_HTTP", "Primary RPC endpoint"),
        ("BIRDEYE_API_KEY", "Birdeye API key"),
        ("YELLOWSTONE_GRPC_HTTP", "Yellowstone gRPC endpoint"),
    ];
    
    for (var, desc) in critical_vars {
        match env::var(var) {
            Ok(value) => {
                if value.contains("YOUR_ACTUAL") || value.contains("PLACEHOLDER") {
                    println!("  ❌ {}: Contains placeholder value", var);
                    passed = false;
                } else {
                    let masked = mask_sensitive(&value);
                    println!("  ✅ {}: {} ({})", var, desc, masked);
                }
            }
            Err(_) => {
                println!("  ❌ {}: Not set ({})", var, desc);
                passed = false;
            }
        }
    }
    
    // Trading parameters
    let trading_vars = [
        ("DEFAULT_TRADE_AMOUNT", "0.05"),
        ("MAX_SLIPPAGE_BPS", "100"),
        ("MIN_PROFIT_THRESHOLD_BPS", "75"),
    ];
    
    for (var, default) in trading_vars {
        let value = env::var(var).unwrap_or_else(|_| default.to_string());
        println!("  ✅ {}: {}", var, value);
    }
    
    Ok(passed)
}

async fn validate_api_connectivity() -> Result<bool> {
    println!("\n🌐 Validating API Connectivity...");
    let mut passed = true;
    
    // Test RPC endpoint
    match test_rpc_connection().await {
        Ok(_) => println!("  ✅ RPC endpoint: Connection successful"),
        Err(e) => {
            println!("  ❌ RPC endpoint: Connection failed - {}", e);
            passed = false;
        }
    }
    
    // Test Birdeye API
    match test_birdeye_api().await {
        Ok(_) => println!("  ✅ Birdeye API: Connection successful"),
        Err(e) => {
            println!("  ❌ Birdeye API: Connection failed - {}", e);
            passed = false;
        }
    }
    
    Ok(passed)
}

async fn validate_wallet_setup() -> Result<bool> {
    println!("\n💰 Validating Wallet Setup...");
    let mut passed = true;
    
    // Check private key format
    match env::var("PRIVATE_KEY") {
        Ok(private_key) => {
            if private_key.len() < 50 {
                println!("  ❌ Private key appears too short");
                passed = false;
            } else if private_key.contains("YOUR_ACTUAL") {
                println!("  ❌ Private key is still a placeholder");
                passed = false;
            } else {
                println!("  ✅ Private key format appears valid");
                
                // Test wallet balance (simplified check)
                match test_wallet_balance().await {
                    Ok(balance) => {
                        if balance < 0.1 {
                            println!("  ⚠️  Wallet balance low: {:.4} SOL", balance);
                            println!("     Consider adding more SOL for gas fees");
                        } else {
                            println!("  ✅ Wallet balance: {:.4} SOL", balance);
                        }
                    }
                    Err(e) => {
                        println!("  ❌ Could not check wallet balance: {}", e);
                        passed = false;
                    }
                }
            }
        }
        Err(_) => {
            println!("  ❌ Private key not set");
            passed = false;
        }
    }
    
    Ok(passed)
}

async fn validate_trading_config() -> Result<bool> {
    println!("\n⚙️ Validating Trading Configuration...");
    let mut passed = true;
    
    // Check trade amount
    let trade_amount: f64 = env::var("DEFAULT_TRADE_AMOUNT")
        .unwrap_or_else(|_| "0.05".to_string())
        .parse()
        .unwrap_or(0.05);
    
    if trade_amount <= 0.0 {
        println!("  ❌ Trade amount must be greater than 0");
        passed = false;
    } else if trade_amount > 10.0 {
        println!("  ⚠️  Trade amount is high: {} SOL", trade_amount);
        println!("     Consider starting with a smaller amount");
    } else {
        println!("  ✅ Trade amount: {} SOL", trade_amount);
    }
    
    // Check slippage
    let slippage: u64 = env::var("MAX_SLIPPAGE_BPS")
        .unwrap_or_else(|_| "100".to_string())
        .parse()
        .unwrap_or(100);
    
    if slippage > 2000 {
        println!("  ❌ Slippage too high: {}bps (max 2000bps)", slippage);
        passed = false;
    } else {
        println!("  ✅ Max slippage: {}bps ({}%)", slippage, slippage as f64 / 100.0);
    }
    
    Ok(passed)
}

async fn validate_safety_config() -> Result<bool> {
    println!("\n🛡️ Validating Safety Configuration...");
    let mut passed = true;
    
    // Check emergency stop
    let emergency_stop: bool = env::var("EMERGENCY_STOP")
        .unwrap_or_else(|_| "false".to_string())
        .parse()
        .unwrap_or(false);
    
    if emergency_stop {
        println!("  ❌ Emergency stop is enabled - live trading disabled");
        passed = false;
    } else {
        println!("  ✅ Emergency stop: Disabled (live trading enabled)");
    }
    
    // Check test mode
    let test_mode: bool = env::var("TEST_MODE")
        .unwrap_or_else(|_| "false".to_string())
        .parse()
        .unwrap_or(false);
    
    if test_mode {
        println!("  ⚠️  Test mode is enabled");
    } else {
        println!("  ✅ Test mode: Disabled (production mode)");
    }
    
    // Check dry run
    let dry_run: bool = env::var("DRY_RUN")
        .unwrap_or_else(|_| "false".to_string())
        .parse()
        .unwrap_or(false);
    
    if dry_run {
        println!("  ⚠️  Dry run mode is enabled - no real transactions");
    } else {
        println!("  ✅ Dry run: Disabled (real transactions enabled)");
    }
    
    Ok(passed)
}

async fn test_rpc_connection() -> Result<()> {
    use solana_client::nonblocking::rpc_client::RpcClient;
    
    let rpc_url = env::var("RPC_HTTP")?;
    let client = RpcClient::new(rpc_url);
    
    let _version = client.get_version().await
        .map_err(|e| anyhow!("RPC connection failed: {}", e))?;
    
    Ok(())
}

async fn test_birdeye_api() -> Result<()> {
    use reqwest::Client;
    
    let api_key = env::var("BIRDEYE_API_KEY")?;
    let client = Client::new();
    
    let response = client
        .get("https://public-api.birdeye.so/defi/price")
        .header("X-API-KEY", &api_key)
        .query(&[("address", "So11111111111111111111111111111111111111112")])
        .send()
        .await
        .map_err(|e| anyhow!("Birdeye API request failed: {}", e))?;
    
    if !response.status().is_success() {
        return Err(anyhow!("Birdeye API returned error: {}", response.status()));
    }
    
    Ok(())
}

async fn test_wallet_balance() -> Result<f64> {
    use solana_client::nonblocking::rpc_client::RpcClient;
    use solana_sdk::{signature::Keypair, signer::Signer};
    use bs58;
    
    let rpc_url = env::var("RPC_HTTP")?;
    let client = RpcClient::new(rpc_url);
    
    let private_key = env::var("PRIVATE_KEY")?;
    let keypair_bytes = bs58::decode(&private_key)
        .into_vec()
        .map_err(|_| anyhow!("Invalid private key format"))?;
    
    let keypair = Keypair::from_bytes(&keypair_bytes)
        .map_err(|_| anyhow!("Could not create keypair from private key"))?;
    
    let balance = client.get_balance(&keypair.pubkey()).await
        .map_err(|e| anyhow!("Could not get wallet balance: {}", e))?;
    
    Ok(balance as f64 / 1_000_000_000.0)
}

fn mask_sensitive(value: &str) -> String {
    if value.len() <= 8 {
        "*".repeat(value.len())
    } else {
        format!("{}...{}", &value[..4], &value[value.len()-4..])
    }
}
