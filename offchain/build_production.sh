#!/bin/bash

# Production Build Script for Solana Arbitrage Bot - Live Trading System

echo "🚀 Building Solana Arbitrage Bot for Live Trading..."
echo "═══════════════════════════════════════════════════"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found!"
    echo "Please create .env file with your configuration before building."
    exit 1
fi

# Check for placeholder values in .env
if grep -q "YOUR_ACTUAL_PRIVATE_KEY_HERE" .env; then
    echo "❌ Error: Private key is still a placeholder!"
    echo "Please update PRIVATE_KEY in .env with your actual private key."
    exit 1
fi

# Set optimization flags for production build
export RUSTFLAGS="-C target-cpu=native -C opt-level=3 -C lto=fat"
export RUST_LOG=info

echo "🔧 Compiling with production optimizations..."

# Build in release mode with all optimizations
cargo build --release

if [ $? -eq 0 ]; then
    echo "✅ Build completed successfully!"
    echo "📦 Binary location: target/release/solana-vntr-sniper"
    echo ""
    echo "🔍 Next steps:"
    echo "1. Validate setup: cargo run --bin validate_live_setup"
    echo "2. Test configuration: cargo run --release -- live-trade --dry-run"
    echo "3. Start live trading: cargo run --release -- live-trade"
    echo ""
    echo "⚠️  IMPORTANT: Always test with --dry-run first!"
    echo "⚠️  IMPORTANT: Monitor your trades closely during live trading!"
    echo ""
    echo "🛡️  Safety reminders:"
    echo "   - Start with small trade amounts"
    echo "   - Keep emergency stop procedures ready"
    echo "   - Monitor wallet balance and positions"
    echo "   - Have sufficient SOL for gas fees"
else
    echo "❌ Build failed!"
    exit 1
fi
