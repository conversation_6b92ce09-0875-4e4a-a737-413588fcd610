//! Performance validation tests for the arbitrage bot improvements
//! 
//! This module tests the core performance improvements:
//! 1. Semaphore-based rate limiting
//! 2. Circuit breaker functionality
//! 3. Lock-free global state management
//! 4. Retry logic with exponential backoff

use std::sync::Arc;
use std::sync::atomic::AtomicU32;
use std::time::{Duration, Instant};
use tokio::time::sleep;
use anyhow::Result;

use crate::core::{
    rate_limiter::{RateLimiter, RateLimiterConfig},
    circuit_breaker::{CircuitBreaker, CircuitBreakerConfig},
    lock_free_globals::{LockFreeGlobals, TokenTrackingInfo},
    retry::{RetryConfig, RetryableError, NonRetryableError, retry_with_backoff},
};

/// Test semaphore-based rate limiting performance
#[tokio::test]
async fn test_rate_limiter_performance() -> Result<()> {
    println!("🧪 Testing Rate Limiter Performance...");
    
    let config = RateLimiterConfig {
        max_concurrent_rpc_calls: 5,
        max_concurrent_swaps: 2,
        max_concurrent_price_checks: 10,
        max_concurrent_pool_discovery: 3,
    };
    
    let rate_limiter = Arc::new(RateLimiter::new(config));
    let start_time = Instant::now();
    
    // Test concurrent RPC calls
    let mut handles = Vec::new();
    for i in 0..20 {
        let limiter = rate_limiter.clone();
        let handle = tokio::spawn(async move {
            let result = limiter.execute_rpc(|| async {
                // Simulate RPC call
                sleep(Duration::from_millis(100)).await;
                println!("RPC call {} completed", i);
                Ok::<(), anyhow::Error>(())
            }).await;
            result
        });
        handles.push(handle);
    }
    
    // Wait for all tasks to complete
    for handle in handles {
        let _ = handle.await?;
    }
    
    let elapsed = start_time.elapsed();
    println!("✅ Rate limiter test completed in {:?}", elapsed);
    
    // With 5 concurrent slots and 20 tasks taking 100ms each,
    // we expect roughly 400ms total time (4 batches of 5)
    assert!(elapsed >= Duration::from_millis(350));
    assert!(elapsed <= Duration::from_millis(600));
    
    Ok(())
}

/// Test circuit breaker functionality and recovery
#[tokio::test]
async fn test_circuit_breaker_performance() -> Result<()> {
    println!("🧪 Testing Circuit Breaker Performance...");
    
    let config = CircuitBreakerConfig {
        failure_threshold: 3,
        recovery_timeout: Duration::from_millis(500),
        half_open_max_calls: 2,
        half_open_success_threshold: 1,
    };
    
    let circuit_breaker = Arc::new(CircuitBreaker::new("test_service".to_string(), config));
    
    // Test 1: Normal operation (should be fast)
    let start_time = Instant::now();
    for i in 0..5 {
        let result = circuit_breaker.execute(|| async {
            sleep(Duration::from_millis(10)).await;
            Ok::<(), anyhow::Error>(())
        }).await;
        assert!(result.is_ok());
        println!("Successful call {} completed", i);
    }
    let normal_elapsed = start_time.elapsed();
    println!("✅ Normal operations completed in {:?}", normal_elapsed);
    
    // Test 2: Trigger circuit breaker with failures
    let start_time = Instant::now();
    for i in 0..4 {
        let result = circuit_breaker.execute(|| async {
            sleep(Duration::from_millis(10)).await;
            Err::<(), anyhow::Error>(anyhow::anyhow!("Simulated failure"))
        }).await;
        assert!(result.is_err());
        println!("Failed call {} completed", i);
    }

    // Circuit should now be open - calls should fail fast
    let fast_fail_start = Instant::now();
    let result = circuit_breaker.execute(|| async {
        sleep(Duration::from_millis(100)).await; // This shouldn't execute
        Ok::<(), anyhow::Error>(())
    }).await;
    let fast_fail_elapsed = fast_fail_start.elapsed();
    
    assert!(result.is_err());
    assert!(fast_fail_elapsed < Duration::from_millis(50)); // Should fail fast
    println!("✅ Fast failure completed in {:?}", fast_fail_elapsed);
    
    // Test 3: Recovery after timeout
    sleep(Duration::from_millis(600)).await; // Wait for recovery timeout
    
    let recovery_start = Instant::now();
    let result = circuit_breaker.execute(|| async {
        sleep(Duration::from_millis(10)).await;
        Ok::<(), anyhow::Error>(())
    }).await;
    let recovery_elapsed = recovery_start.elapsed();
    
    assert!(result.is_ok());
    println!("✅ Recovery test completed in {:?}", recovery_elapsed);
    
    Ok(())
}

/// Test lock-free global state performance
#[tokio::test]
async fn test_lock_free_globals_performance() -> Result<()> {
    println!("🧪 Testing Lock-Free Globals Performance...");
    
    let globals = Arc::new(LockFreeGlobals::from_env());
    let start_time = Instant::now();
    
    // Test concurrent access to token tracking
    let mut handles = Vec::new();
    for i in 0..100 {
        let globals_clone = globals.clone();
        let handle = tokio::spawn(async move {
            let token_id = format!("token_{}", i % 10); // 10 different tokens
            
            // Insert/update token info
            let info = TokenTrackingInfo {
                top_pnl: i as f64 * 0.1,
                last_price_check: i as u64,
                price_history: vec![(i as f64, i as u64)],
                buy_price: i as f64,
                buy_timestamp: i as u64,
                position_size: i as f64,
                status: crate::core::lock_free_globals::TokenStatus::Monitoring,
            };
            
            globals_clone.token_tracking.insert(token_id.clone(), info);
            
            // Read token info
            if let Some(stored_info) = globals_clone.token_tracking.get(&token_id) {
                assert_eq!(stored_info.top_pnl, i as f64 * 0.1);
            }
            
            // Update atomic flags
            globals_clone.buying_enabled.store(i % 2 == 0, std::sync::atomic::Ordering::Relaxed);
            let enabled = globals_clone.buying_enabled.load(std::sync::atomic::Ordering::Relaxed);
            
            // Update position size
            globals_clone.total_position_size.fetch_add(i, std::sync::atomic::Ordering::Relaxed);
        });
        handles.push(handle);
    }
    
    // Wait for all tasks to complete
    for handle in handles {
        handle.await?;
    }
    
    let elapsed = start_time.elapsed();
    println!("✅ Lock-free globals test completed in {:?}", elapsed);
    
    // Verify final state
    assert_eq!(globals.token_tracking.len(), 10); // 10 unique tokens
    let total_position = globals.total_position_size.load(std::sync::atomic::Ordering::Relaxed);
    assert!(total_position > 0);
    
    // Lock-free operations should be very fast
    assert!(elapsed < Duration::from_millis(100));
    
    Ok(())
}

/// Test retry logic performance and behavior
#[tokio::test]
async fn test_retry_logic_performance() -> Result<()> {
    println!("🧪 Testing Retry Logic Performance...");
    
    let config = RetryConfig {
        max_attempts: 3,
        base_delay: Duration::from_millis(50),
        max_delay: Duration::from_millis(500),
        backoff_multiplier: 2.0,
        jitter: false, // Disable jitter for predictable timing
    };
    
    // Test 1: Successful operation (no retries)
    let start_time = Instant::now();
    let result = retry_with_backoff(
        &config,
        || async { Ok::<i32, anyhow::Error>(42) },
    ).await;
    let success_elapsed = start_time.elapsed();
    
    assert_eq!(result.unwrap(), 42);
    assert!(success_elapsed < Duration::from_millis(20)); // Should be immediate
    println!("✅ Successful operation completed in {:?}", success_elapsed);
    
    // Test 2: Retryable error (should retry with backoff)
    let attempt_count = Arc::new(AtomicU32::new(0));
    let start_time = Instant::now();
    let attempt_count_clone = attempt_count.clone();
    let result = retry_with_backoff(
        &config,
        || {
            let count = attempt_count_clone.clone();
            async move {
                let current_attempt = count.fetch_add(1, std::sync::atomic::Ordering::SeqCst) + 1;
                if current_attempt < 3 {
                    Err(anyhow::anyhow!("Simulated network error"))
                } else {
                    Ok::<i32, anyhow::Error>(42)
                }
            }
        },
    ).await;
    let retry_elapsed = start_time.elapsed();

    assert_eq!(result.unwrap(), 42);
    assert_eq!(attempt_count.load(std::sync::atomic::Ordering::SeqCst), 3);
    // Should take at least 50ms + 100ms = 150ms for backoff delays
    assert!(retry_elapsed >= Duration::from_millis(140));
    assert!(retry_elapsed <= Duration::from_millis(200));
    println!("✅ Retry operation completed in {:?} with {} attempts", retry_elapsed, attempt_count.load(std::sync::atomic::Ordering::SeqCst));
    
    // Test 3: Non-retryable error (should fail immediately)
    let start_time = Instant::now();
    let result = retry_with_backoff(
        &RetryConfig {
            max_attempts: 1, // Only one attempt for non-retryable
            ..config
        },
        || async {
            Err::<i32, anyhow::Error>(anyhow::anyhow!("Non-retryable error"))
        },
    ).await;
    let fail_elapsed = start_time.elapsed();
    
    assert!(result.is_err());
    assert!(fail_elapsed < Duration::from_millis(20)); // Should fail fast
    println!("✅ Non-retryable error handled in {:?}", fail_elapsed);
    
    Ok(())
}

/// Comprehensive performance benchmark
#[tokio::test]
async fn test_integrated_performance_benchmark() -> Result<()> {
    println!("🧪 Running Integrated Performance Benchmark...");
    
    let rate_limiter = Arc::new(RateLimiter::new(RateLimiterConfig::default()));
    let circuit_breaker = Arc::new(CircuitBreaker::new(
        "benchmark".to_string(),
        CircuitBreakerConfig::default(),
    ));
    let globals = Arc::new(LockFreeGlobals::from_env());
    
    let start_time = Instant::now();
    let mut handles = Vec::new();
    
    // Simulate realistic workload with all components
    for i in 0..50 {
        let rate_limiter = rate_limiter.clone();
        let circuit_breaker = circuit_breaker.clone();
        let globals = globals.clone();
        
        let handle = tokio::spawn(async move {
            // Rate-limited operation with circuit-breaker protection
            let result = rate_limiter.execute_rpc(|| async {
                circuit_breaker.execute(|| async {
                    // Update global state
                    let token_id = format!("benchmark_token_{}", i % 5);
                    let info = TokenTrackingInfo {
                        top_pnl: i as f64 * 0.01,
                        last_price_check: i as u64,
                        price_history: vec![(100.0 + (i as f64 * 0.1), i as u64)],
                        buy_price: 100.0,
                        buy_timestamp: i as u64,
                        position_size: 1.0,
                        status: crate::core::lock_free_globals::TokenStatus::Monitoring,
                    };

                    globals.token_tracking.insert(token_id, info);
                    globals.total_position_size.fetch_add(1, std::sync::atomic::Ordering::Relaxed);

                    // Simulate some work
                    sleep(Duration::from_millis(10)).await;
                    Ok::<(), anyhow::Error>(())
                }).await
            }).await;

            result
        });
        handles.push(handle);
    }
    
    // Wait for all operations to complete
    let mut success_count = 0;
    for handle in handles {
        if handle.await?.is_ok() {
            success_count += 1;
        }
    }
    
    let elapsed = start_time.elapsed();
    println!("✅ Integrated benchmark completed in {:?}", elapsed);
    println!("   Successful operations: {}/50", success_count);
    println!("   Average time per operation: {:?}", elapsed / 50);
    
    // Verify system state
    assert!(globals.token_tracking.len() <= 5); // Should have 5 unique tokens
    let total_position = globals.total_position_size.load(std::sync::atomic::Ordering::Relaxed);
    assert_eq!(total_position as usize, success_count);
    
    Ok(())
}
