// DEPRECATED: This file is being replaced by core::lock_free_globals
// This file is kept for backward compatibility during migration
// New code should use crate::core::lock_free_globals::LockFreeGlobals

use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use lazy_static::lazy_static;
use tokio::time::Instant;
use once_cell::sync::Lazy;

// Import the new lock-free globals
use crate::core::lock_free_globals::LockFreeGlobals;

#[derive(Clone, Debug)]
pub struct TokenTrackingInfo {
    pub top_pnl: f64,
    pub last_price_check: Instant,
    pub price_history: Vec<(f64, Instant)>,  // Store price history with timestamps
}

// DEPRECATED: Use LockFreeGlobals instead
// These are kept for backward compatibility during migration
lazy_static! {
    pub static ref TOKEN_TRACKING: Arc<Mutex<HashMap<String, TokenTrackingInfo>>> = Arc::new(Mutex::new(HashMap::new()));
    pub static ref BUYING_ENABLED: Mutex<bool> = Mutex::new(true);
    pub static ref MAX_WAIT_TIME: Mutex<u64> = Mutex::new(300000); // 5 minutes in milliseconds
}

// NEW: Lock-free global state instance
pub static LOCK_FREE_GLOBALS: Lazy<Arc<LockFreeGlobals>> = Lazy::new(|| {
    Arc::new(LockFreeGlobals::from_env())
});

// Constants
pub const LOG_INSTRUCTION: bool = true;