//! Market Depth Analysis System
//! 
//! This module provides comprehensive market depth analysis including order book depth,
//! liquidity assessment at different price levels, and optimal trade size calculations
//! based on available market depth across multiple DEXes.

use std::{
    collections::HashMap,
    sync::Arc,
};
use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use anchor_client::solana_sdk::pubkey::Pubkey;
use colored::Colorize;

use crate::core::{
    pool_monitor::{PoolState, PoolMonitor},
    app_state::EnhancedAppState,
};

/// Market depth information for a token pair
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketDepth {
    pub token_a: Pubkey,
    pub token_b: Pubkey,
    pub total_liquidity_a: u64,
    pub total_liquidity_b: u64,
    pub depth_levels: Vec<DepthLevel>,
    pub best_bid: Option<f64>,
    pub best_ask: Option<f64>,
    pub spread_bps: u16, // Spread in basis points
    pub last_updated: u64,
}

/// Depth level showing liquidity at specific price ranges
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DepthLevel {
    pub price_level: f64,
    pub cumulative_liquidity_a: u64,
    pub cumulative_liquidity_b: u64,
    pub dex_contributions: Vec<DexContribution>,
}

/// DEX contribution to liquidity at a specific level
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DexContribution {
    pub dex_name: String,
    pub pool_id: Pubkey,
    pub liquidity_a: u64,
    pub liquidity_b: u64,
    pub price: f64,
    pub fee_rate: f64,
}

/// Trade size recommendation based on market depth
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeSizeRecommendation {
    pub token_pair: (Pubkey, Pubkey),
    pub direction: TradeDirection,
    pub recommended_size: u64,
    pub max_size_low_impact: u64,   // Size with <0.5% price impact
    pub max_size_medium_impact: u64, // Size with <2% price impact
    pub max_size_high_impact: u64,   // Size with <5% price impact
    pub expected_price_impact: f64,
    pub optimal_dex_route: Vec<DexRoute>,
}

/// Trade direction
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum TradeDirection {
    AtoB, // Sell token A, buy token B
    BtoA, // Sell token B, buy token A
}

/// Optimal DEX routing for a trade
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DexRoute {
    pub dex_name: String,
    pub pool_id: Pubkey,
    pub allocation_percentage: f64,
    pub expected_output: u64,
    pub price_impact: f64,
}

/// Market depth analysis configuration
#[derive(Debug, Clone)]
pub struct MarketDepthConfig {
    pub price_levels: usize,        // Number of price levels to analyze
    pub max_price_deviation: f64,   // Maximum price deviation from mid-price (%)
    pub min_liquidity_threshold: u64, // Minimum liquidity to consider
    pub update_interval_ms: u64,
    pub low_impact_threshold: f64,   // Price impact threshold for low impact (%)
    pub medium_impact_threshold: f64, // Price impact threshold for medium impact (%)
    pub high_impact_threshold: f64,  // Price impact threshold for high impact (%)
}

impl Default for MarketDepthConfig {
    fn default() -> Self {
        Self {
            price_levels: 20,
            max_price_deviation: 10.0, // 10%
            min_liquidity_threshold: 1000, // Minimum 1000 tokens
            update_interval_ms: 5000, // 5 seconds
            low_impact_threshold: 0.5,   // 0.5%
            medium_impact_threshold: 2.0, // 2%
            high_impact_threshold: 5.0,   // 5%
        }
    }
}

/// Market depth analyzer
pub struct MarketDepthAnalyzer {
    config: MarketDepthConfig,
    pool_monitor: Arc<PoolMonitor>,
    app_state: Arc<EnhancedAppState>,
    depth_cache: Arc<tokio::sync::RwLock<HashMap<(Pubkey, Pubkey), MarketDepth>>>,
}

impl MarketDepthAnalyzer {
    /// Create a new market depth analyzer
    pub fn new(
        config: MarketDepthConfig,
        pool_monitor: Arc<PoolMonitor>,
        app_state: Arc<EnhancedAppState>,
    ) -> Self {
        Self {
            config,
            pool_monitor,
            app_state,
            depth_cache: Arc::new(tokio::sync::RwLock::new(HashMap::new())),
        }
    }

    /// Start the market depth analyzer
    pub async fn start(&self) -> Result<()> {
        println!("{}", "📊 Starting Market Depth Analyzer".magenta().bold());
        println!("{}", "═".repeat(60).magenta());

        // Start periodic depth analysis
        self.start_periodic_analysis().await?;

        println!("✅ Market Depth Analyzer started successfully");
        Ok(())
    }

    /// Analyze market depth for a token pair
    pub async fn analyze_depth(&self, token_a: &Pubkey, token_b: &Pubkey) -> Result<MarketDepth> {
        // Get all pools for this token pair
        let pools = self.pool_monitor.get_pools_by_tokens(token_a, token_b).await;
        
        if pools.is_empty() {
            return Err(anyhow!("No pools found for token pair {}/{}", token_a, token_b));
        }

        // Calculate total liquidity
        let total_liquidity_a: u64 = pools.iter().map(|p| p.token_a_balance).sum();
        let total_liquidity_b: u64 = pools.iter().map(|p| p.token_b_balance).sum();

        // Calculate weighted average price
        let total_value_a: f64 = pools.iter()
            .map(|p| p.token_a_balance as f64 * p.current_price)
            .sum();
        let mid_price = if total_liquidity_a > 0 {
            total_value_a / total_liquidity_a as f64
        } else {
            pools.iter().map(|p| p.current_price).sum::<f64>() / pools.len() as f64
        };

        // Generate depth levels
        let depth_levels = self.generate_depth_levels(&pools, mid_price).await?;

        // Calculate spread
        let (best_bid, best_ask) = self.calculate_best_bid_ask(&pools);
        let spread_bps = if let (Some(bid), Some(ask)) = (best_bid, best_ask) {
            ((ask - bid) / bid * 10000.0) as u16
        } else {
            0
        };

        let market_depth = MarketDepth {
            token_a: *token_a,
            token_b: *token_b,
            total_liquidity_a,
            total_liquidity_b,
            depth_levels,
            best_bid,
            best_ask,
            spread_bps,
            last_updated: chrono::Utc::now().timestamp_millis() as u64,
        };

        // Cache the result
        let mut cache = self.depth_cache.write().await;
        cache.insert((*token_a, *token_b), market_depth.clone());

        Ok(market_depth)
    }

    /// Get trade size recommendation for a token pair
    pub async fn get_trade_size_recommendation(
        &self,
        token_a: &Pubkey,
        token_b: &Pubkey,
        direction: TradeDirection,
        desired_amount: u64,
    ) -> Result<TradeSizeRecommendation> {
        let market_depth = self.analyze_depth(token_a, token_b).await?;
        
        // Calculate price impact for different trade sizes
        let (low_impact_size, medium_impact_size, high_impact_size) = 
            self.calculate_impact_sizes(&market_depth, &direction).await?;

        // Find optimal DEX routing
        let optimal_route = self.calculate_optimal_routing(
            &market_depth,
            &direction,
            desired_amount,
        ).await?;

        // Calculate expected price impact for desired amount
        let expected_price_impact = self.calculate_price_impact(
            &market_depth,
            &direction,
            desired_amount,
        ).await?;

        let recommended_size = std::cmp::min(desired_amount, medium_impact_size);

        Ok(TradeSizeRecommendation {
            token_pair: (*token_a, *token_b),
            direction,
            recommended_size,
            max_size_low_impact: low_impact_size,
            max_size_medium_impact: medium_impact_size,
            max_size_high_impact: high_impact_size,
            expected_price_impact,
            optimal_dex_route: optimal_route,
        })
    }

    /// Generate depth levels for analysis
    async fn generate_depth_levels(
        &self,
        pools: &[PoolState],
        mid_price: f64,
    ) -> Result<Vec<DepthLevel>> {
        let mut depth_levels = Vec::new();
        let price_step = (self.config.max_price_deviation / self.config.price_levels as f64) / 100.0;

        for i in 0..self.config.price_levels {
            let price_deviation = (i as f64 * price_step) - (self.config.max_price_deviation / 200.0);
            let price_level = mid_price * (1.0 + price_deviation);

            let mut cumulative_liquidity_a = 0u64;
            let mut cumulative_liquidity_b = 0u64;
            let mut dex_contributions = Vec::new();

            for pool in pools {
                if pool.liquidity < self.config.min_liquidity_threshold {
                    continue;
                }

                // Calculate effective liquidity at this price level
                let effective_liquidity_a = self.calculate_effective_liquidity(
                    pool,
                    price_level,
                    TradeDirection::AtoB,
                );
                let effective_liquidity_b = self.calculate_effective_liquidity(
                    pool,
                    price_level,
                    TradeDirection::BtoA,
                );

                if effective_liquidity_a > 0 || effective_liquidity_b > 0 {
                    cumulative_liquidity_a += effective_liquidity_a;
                    cumulative_liquidity_b += effective_liquidity_b;

                    dex_contributions.push(DexContribution {
                        dex_name: pool.dex_name.clone(),
                        pool_id: pool.pool_id,
                        liquidity_a: effective_liquidity_a,
                        liquidity_b: effective_liquidity_b,
                        price: pool.current_price,
                        fee_rate: pool.fee_rate,
                    });
                }
            }

            depth_levels.push(DepthLevel {
                price_level,
                cumulative_liquidity_a,
                cumulative_liquidity_b,
                dex_contributions,
            });
        }

        Ok(depth_levels)
    }

    /// Calculate effective liquidity for a pool at a specific price level
    fn calculate_effective_liquidity(
        &self,
        pool: &PoolState,
        target_price: f64,
        direction: TradeDirection,
    ) -> u64 {
        // Simplified liquidity calculation
        // In reality, this would use the specific AMM formula for each DEX type
        let price_ratio = target_price / pool.current_price;
        
        match direction {
            TradeDirection::AtoB => {
                if price_ratio <= 1.0 {
                    // Price is favorable for selling A
                    (pool.token_a_balance as f64 * price_ratio) as u64
                } else {
                    // Price is unfavorable, reduce effective liquidity
                    (pool.token_a_balance as f64 / price_ratio) as u64
                }
            }
            TradeDirection::BtoA => {
                if price_ratio >= 1.0 {
                    // Price is favorable for selling B
                    (pool.token_b_balance as f64 / price_ratio) as u64
                } else {
                    // Price is unfavorable, reduce effective liquidity
                    (pool.token_b_balance as f64 * price_ratio) as u64
                }
            }
        }
    }

    /// Calculate best bid and ask prices
    fn calculate_best_bid_ask(&self, pools: &[PoolState]) -> (Option<f64>, Option<f64>) {
        if pools.is_empty() {
            return (None, None);
        }

        // For AMM pools, bid/ask is derived from the current price and fees
        let prices: Vec<f64> = pools.iter().map(|p| p.current_price).collect();
        let min_price = prices.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_price = prices.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));

        // Apply average fee as spread
        let avg_fee = pools.iter().map(|p| p.fee_rate).sum::<f64>() / pools.len() as f64;
        let mid_price = (min_price + max_price) / 2.0;
        
        let best_bid = Some(mid_price * (1.0 - avg_fee / 2.0));
        let best_ask = Some(mid_price * (1.0 + avg_fee / 2.0));

        (best_bid, best_ask)
    }

    /// Calculate trade sizes for different impact levels
    async fn calculate_impact_sizes(
        &self,
        market_depth: &MarketDepth,
        direction: &TradeDirection,
    ) -> Result<(u64, u64, u64)> {
        let mut low_impact_size = 0u64;
        let mut medium_impact_size = 0u64;
        let mut high_impact_size = 0u64;

        // Iterate through depth levels to find impact thresholds
        for level in &market_depth.depth_levels {
            let available_liquidity = match direction {
                TradeDirection::AtoB => level.cumulative_liquidity_a,
                TradeDirection::BtoA => level.cumulative_liquidity_b,
            };

            let price_impact = ((level.price_level - market_depth.best_bid.unwrap_or(level.price_level)) 
                / market_depth.best_bid.unwrap_or(level.price_level)).abs() * 100.0;

            if price_impact <= self.config.low_impact_threshold && low_impact_size == 0 {
                low_impact_size = available_liquidity;
            }
            if price_impact <= self.config.medium_impact_threshold && medium_impact_size == 0 {
                medium_impact_size = available_liquidity;
            }
            if price_impact <= self.config.high_impact_threshold && high_impact_size == 0 {
                high_impact_size = available_liquidity;
            }
        }

        // Ensure sizes are in ascending order
        medium_impact_size = medium_impact_size.max(low_impact_size);
        high_impact_size = high_impact_size.max(medium_impact_size);

        Ok((low_impact_size, medium_impact_size, high_impact_size))
    }

    /// Calculate optimal DEX routing for a trade
    async fn calculate_optimal_routing(
        &self,
        market_depth: &MarketDepth,
        direction: &TradeDirection,
        amount: u64,
    ) -> Result<Vec<DexRoute>> {
        let mut routes = Vec::new();
        let mut remaining_amount = amount;

        // Sort pools by best price (considering fees)
        let mut pool_scores: Vec<(String, Pubkey, f64, f64)> = Vec::new();
        
        for level in &market_depth.depth_levels {
            for contribution in &level.dex_contributions {
                let effective_price = match direction {
                    TradeDirection::AtoB => contribution.price * (1.0 + contribution.fee_rate),
                    TradeDirection::BtoA => contribution.price * (1.0 - contribution.fee_rate),
                };
                
                pool_scores.push((
                    contribution.dex_name.clone(),
                    contribution.pool_id,
                    effective_price,
                    contribution.fee_rate,
                ));
            }
        }

        // Sort by best effective price
        pool_scores.sort_by(|a, b| {
            match direction {
                TradeDirection::AtoB => a.2.partial_cmp(&b.2).unwrap_or(std::cmp::Ordering::Equal),
                TradeDirection::BtoA => b.2.partial_cmp(&a.2).unwrap_or(std::cmp::Ordering::Equal),
            }
        });

        // Allocate trade across best pools
        for (dex_name, pool_id, price, fee_rate) in pool_scores {
            if remaining_amount == 0 {
                break;
            }

            // Calculate how much we can trade through this pool
            let pool_capacity = self.get_pool_capacity(&pool_id, direction, &market_depth).await?;
            let allocation = std::cmp::min(remaining_amount, pool_capacity);
            
            if allocation > 0 {
                let allocation_percentage = (allocation as f64 / amount as f64) * 100.0;
                let expected_output = (allocation as f64 * price) as u64;
                let price_impact = self.calculate_single_pool_impact(allocation, pool_capacity);

                routes.push(DexRoute {
                    dex_name,
                    pool_id,
                    allocation_percentage,
                    expected_output,
                    price_impact,
                });

                remaining_amount -= allocation;
            }
        }

        Ok(routes)
    }

    /// Get pool capacity for a specific direction
    async fn get_pool_capacity(
        &self,
        pool_id: &Pubkey,
        direction: &TradeDirection,
        market_depth: &MarketDepth,
    ) -> Result<u64> {
        // Find the pool in depth levels
        for level in &market_depth.depth_levels {
            for contribution in &level.dex_contributions {
                if contribution.pool_id == *pool_id {
                    return Ok(match direction {
                        TradeDirection::AtoB => contribution.liquidity_a,
                        TradeDirection::BtoA => contribution.liquidity_b,
                    });
                }
            }
        }
        
        Ok(0)
    }

    /// Calculate price impact for a specific amount
    async fn calculate_price_impact(
        &self,
        market_depth: &MarketDepth,
        direction: &TradeDirection,
        amount: u64,
    ) -> Result<f64> {
        // Simplified price impact calculation
        let total_liquidity = match direction {
            TradeDirection::AtoB => market_depth.total_liquidity_a,
            TradeDirection::BtoA => market_depth.total_liquidity_b,
        };

        if total_liquidity == 0 {
            return Ok(100.0); // 100% impact if no liquidity
        }

        // Basic price impact formula: impact = (amount / liquidity) * 100
        let impact = (amount as f64 / total_liquidity as f64) * 100.0;
        Ok(impact.min(100.0)) // Cap at 100%
    }

    /// Calculate price impact for a single pool
    fn calculate_single_pool_impact(&self, trade_amount: u64, pool_capacity: u64) -> f64 {
        if pool_capacity == 0 {
            return 100.0;
        }
        
        (trade_amount as f64 / pool_capacity as f64) * 100.0
    }

    /// Start periodic depth analysis
    async fn start_periodic_analysis(&self) -> Result<()> {
        // TODO: Implement periodic analysis for tracked token pairs
        println!("📊 Periodic market depth analysis started");
        Ok(())
    }

    /// Get cached market depth
    pub async fn get_cached_depth(&self, token_a: &Pubkey, token_b: &Pubkey) -> Option<MarketDepth> {
        let cache = self.depth_cache.read().await;
        cache.get(&(*token_a, *token_b)).cloned()
    }

    /// Get depth analysis statistics
    pub async fn get_stats(&self) -> HashMap<String, u64> {
        let cache = self.depth_cache.read().await;
        let mut stats = HashMap::new();
        
        stats.insert("cached_pairs".to_string(), cache.len() as u64);
        stats.insert("total_depth_levels".to_string(), 
            cache.values().map(|d| d.depth_levels.len() as u64).sum());
        
        stats
    }
}
