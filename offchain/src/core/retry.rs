use std::time::Duration;
use std::future::Future;
use anyhow::Result;
use tokio::time::sleep;
use prometheus::{Counter, Histogram, register_counter, register_histogram};
use once_cell::sync::Lazy;
use thiserror::Error;

// Prometheus metrics for retry operations
static RETRY_ATTEMPTS_TOTAL: Lazy<Counter> = Lazy::new(|| {
    register_counter!("retry_attempts_total", "Total number of retry attempts").unwrap()
});

static RETRY_SUCCESSES_TOTAL: Lazy<Counter> = Lazy::new(|| {
    register_counter!("retry_successes_total", "Total number of successful retries").unwrap()
});

static RETRY_FAILURES_TOTAL: Lazy<Counter> = Lazy::new(|| {
    register_counter!("retry_failures_total", "Total number of failed retries").unwrap()
});

static RETRY_DURATION: Lazy<Histogram> = Lazy::new(|| {
    register_histogram!("retry_duration_seconds", "Duration of retry operations").unwrap()
});

/// Configuration for retry behavior
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct RetryConfig {
    pub max_attempts: u32,
    pub base_delay: Duration,
    pub max_delay: Duration,
    pub backoff_multiplier: f64,
    pub jitter: bool,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            base_delay: Duration::from_millis(100),
            max_delay: Duration::from_secs(30),
            backoff_multiplier: 2.0,
            jitter: true,
        }
    }
}

impl RetryConfig {
    /// Load configuration from environment variables
    pub fn from_env() -> Self {
        Self {
            max_attempts: std::env::var("MAX_RETRY_ATTEMPTS")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(3),
            base_delay: Duration::from_millis(
                std::env::var("RETRY_BASE_DELAY_MS")
                    .ok()
                    .and_then(|s| s.parse().ok())
                    .unwrap_or(100)
            ),
            max_delay: Duration::from_millis(
                std::env::var("RETRY_MAX_DELAY_MS")
                    .ok()
                    .and_then(|s| s.parse().ok())
                    .unwrap_or(30000)
            ),
            backoff_multiplier: std::env::var("RETRY_BACKOFF_MULTIPLIER")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(2.0),
            jitter: true,
        }
    }

    /// Create a configuration for RPC operations
    pub fn for_rpc() -> Self {
        Self {
            max_attempts: 4,
            base_delay: Duration::from_millis(250),
            max_delay: Duration::from_secs(10),
            backoff_multiplier: 2.0,
            jitter: true,
        }
    }

    /// Create a configuration for swap operations
    pub fn for_swaps() -> Self {
        Self {
            max_attempts: 3,
            base_delay: Duration::from_millis(500),
            max_delay: Duration::from_secs(15),
            backoff_multiplier: 1.5,
            jitter: true,
        }
    }

    /// Create a configuration for price checks
    pub fn for_price_checks() -> Self {
        Self {
            max_attempts: 2,
            base_delay: Duration::from_millis(100),
            max_delay: Duration::from_secs(5),
            backoff_multiplier: 2.0,
            jitter: false,
        }
    }
}

/// Error types that can be retried
#[derive(Error, Debug)]
pub enum RetryableError {
    #[error("Network error: {0}")]
    Network(String),
    
    #[error("Timeout error: {0}")]
    Timeout(String),
    
    #[error("Rate limit error: {0}")]
    RateLimit(String),
    
    #[error("Temporary service error: {0}")]
    TemporaryService(String),
    
    #[error("RPC error: {0}")]
    Rpc(String),
}

/// Error types that should not be retried
#[derive(Error, Debug)]
pub enum NonRetryableError {
    #[error("Authentication error: {0}")]
    Authentication(String),
    
    #[error("Invalid input: {0}")]
    InvalidInput(String),
    
    #[error("Insufficient funds: {0}")]
    InsufficientFunds(String),
    
    #[error("Transaction failed: {0}")]
    TransactionFailed(String),
    
    #[error("Configuration error: {0}")]
    Configuration(String),
}

/// Classify errors to determine if they should be retried
pub fn classify_error(error: &anyhow::Error) -> ErrorClassification {
    let error_str = error.to_string().to_lowercase();
    
    // Network-related errors that should be retried
    if error_str.contains("connection") ||
       error_str.contains("timeout") ||
       error_str.contains("network") ||
       error_str.contains("dns") ||
       error_str.contains("socket") {
        return ErrorClassification::Retryable(RetryableError::Network(error.to_string()));
    }
    
    // Rate limiting errors
    if error_str.contains("rate limit") ||
       error_str.contains("too many requests") ||
       error_str.contains("429") {
        return ErrorClassification::Retryable(RetryableError::RateLimit(error.to_string()));
    }
    
    // RPC-specific errors
    if error_str.contains("rpc") ||
       error_str.contains("jsonrpc") ||
       error_str.contains("server error") ||
       error_str.contains("internal error") {
        return ErrorClassification::Retryable(RetryableError::Rpc(error.to_string()));
    }
    
    // Timeout errors
    if error_str.contains("timeout") ||
       error_str.contains("deadline") {
        return ErrorClassification::Retryable(RetryableError::Timeout(error.to_string()));
    }
    
    // Authentication errors (don't retry)
    if error_str.contains("unauthorized") ||
       error_str.contains("authentication") ||
       error_str.contains("invalid token") ||
       error_str.contains("403") ||
       error_str.contains("401") {
        return ErrorClassification::NonRetryable(NonRetryableError::Authentication(error.to_string()));
    }
    
    // Insufficient funds (don't retry)
    if error_str.contains("insufficient") ||
       error_str.contains("balance") ||
       error_str.contains("funds") {
        return ErrorClassification::NonRetryable(NonRetryableError::InsufficientFunds(error.to_string()));
    }
    
    // Invalid input (don't retry)
    if error_str.contains("invalid") ||
       error_str.contains("malformed") ||
       error_str.contains("bad request") ||
       error_str.contains("400") {
        return ErrorClassification::NonRetryable(NonRetryableError::InvalidInput(error.to_string()));
    }
    
    // Default to retryable for unknown errors
    ErrorClassification::Retryable(RetryableError::TemporaryService(error.to_string()))
}

/// Classification of errors for retry logic
#[derive(Debug)]
pub enum ErrorClassification {
    Retryable(RetryableError),
    NonRetryable(NonRetryableError),
}

/// Retry an operation with exponential backoff
pub async fn retry_with_backoff<F, Fut, T>(
    config: &RetryConfig,
    operation: F,
) -> Result<T>
where
    F: Fn() -> Fut,
    Fut: Future<Output = Result<T>>,
{
    let timer = RETRY_DURATION.start_timer();
    let mut delay = config.base_delay;
    let mut last_error = None;

    for attempt in 1..=config.max_attempts {
        RETRY_ATTEMPTS_TOTAL.inc();
        
        match operation().await {
            Ok(result) => {
                if attempt > 1 {
                    RETRY_SUCCESSES_TOTAL.inc();
                    tracing::info!("Operation succeeded after {} attempts", attempt);
                }
                timer.observe_duration();
                return Ok(result);
            }
            Err(error) => {
                match classify_error(&error) {
                    ErrorClassification::NonRetryable(non_retryable) => {
                        tracing::error!("Non-retryable error: {}", non_retryable);
                        timer.observe_duration();
                        return Err(error);
                    }
                    ErrorClassification::Retryable(retryable) => {
                        tracing::warn!(
                            "Retryable error on attempt {}/{}: {}",
                            attempt,
                            config.max_attempts,
                            retryable
                        );
                        
                        last_error = Some(error);
                        
                        // Don't sleep after the last attempt
                        if attempt < config.max_attempts {
                            let sleep_duration = if config.jitter {
                                add_jitter(delay)
                            } else {
                                delay
                            };
                            
                            tracing::debug!("Sleeping for {:?} before retry", sleep_duration);
                            sleep(sleep_duration).await;
                            
                            // Calculate next delay with exponential backoff
                            delay = std::cmp::min(
                                Duration::from_millis(
                                    (delay.as_millis() as f64 * config.backoff_multiplier) as u64
                                ),
                                config.max_delay,
                            );
                        }
                    }
                }
            }
        }
    }

    RETRY_FAILURES_TOTAL.inc();
    timer.observe_duration();
    
    // All attempts failed
    if let Some(error) = last_error {
        tracing::error!("All {} retry attempts failed", config.max_attempts);
        Err(error)
    } else {
        Err(anyhow::anyhow!("All retry attempts failed with unknown error"))
    }
}

/// Add jitter to delay to prevent thundering herd
fn add_jitter(delay: Duration) -> Duration {
    use rand::Rng;
    let mut rng = rand::thread_rng();
    let jitter_factor = rng.gen_range(0.5..1.5); // ±50% jitter
    Duration::from_millis((delay.as_millis() as f64 * jitter_factor) as u64)
}

/// Retry with a custom error classifier
pub async fn retry_with_custom_classifier<F, Fut, T, C>(
    config: &RetryConfig,
    operation: F,
    classifier: C,
) -> Result<T>
where
    F: Fn() -> Fut,
    Fut: Future<Output = Result<T>>,
    C: Fn(&anyhow::Error) -> bool, // Returns true if error should be retried
{
    let timer = RETRY_DURATION.start_timer();
    let mut delay = config.base_delay;
    let mut last_error = None;

    for attempt in 1..=config.max_attempts {
        RETRY_ATTEMPTS_TOTAL.inc();
        
        match operation().await {
            Ok(result) => {
                if attempt > 1 {
                    RETRY_SUCCESSES_TOTAL.inc();
                    tracing::info!("Operation succeeded after {} attempts", attempt);
                }
                timer.observe_duration();
                return Ok(result);
            }
            Err(error) => {
                if !classifier(&error) {
                    tracing::error!("Non-retryable error: {}", error);
                    timer.observe_duration();
                    return Err(error);
                }
                
                tracing::warn!(
                    "Retryable error on attempt {}/{}: {}",
                    attempt,
                    config.max_attempts,
                    error
                );
                
                last_error = Some(error);
                
                // Don't sleep after the last attempt
                if attempt < config.max_attempts {
                    let sleep_duration = if config.jitter {
                        add_jitter(delay)
                    } else {
                        delay
                    };
                    
                    sleep(sleep_duration).await;
                    
                    // Calculate next delay with exponential backoff
                    delay = std::cmp::min(
                        Duration::from_millis(
                            (delay.as_millis() as f64 * config.backoff_multiplier) as u64
                        ),
                        config.max_delay,
                    );
                }
            }
        }
    }

    RETRY_FAILURES_TOTAL.inc();
    timer.observe_duration();
    
    // All attempts failed
    if let Some(error) = last_error {
        tracing::error!("All {} retry attempts failed", config.max_attempts);
        Err(error)
    } else {
        Err(anyhow::anyhow!("All retry attempts failed with unknown error"))
    }
}

/// Convenience function for retrying RPC operations
pub async fn retry_rpc<F, Fut, T>(operation: F) -> Result<T>
where
    F: Fn() -> Fut,
    Fut: Future<Output = Result<T>>,
{
    retry_with_backoff(&RetryConfig::for_rpc(), operation).await
}

/// Convenience function for retrying swap operations
pub async fn retry_swap<F, Fut, T>(operation: F) -> Result<T>
where
    F: Fn() -> Fut,
    Fut: Future<Output = Result<T>>,
{
    retry_with_backoff(&RetryConfig::for_swaps(), operation).await
}

/// Convenience function for retrying price checks
pub async fn retry_price_check<F, Fut, T>(operation: F) -> Result<T>
where
    F: Fn() -> Fut,
    Fut: Future<Output = Result<T>>,
{
    retry_with_backoff(&RetryConfig::for_price_checks(), operation).await
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::atomic::{AtomicU32, Ordering};
    use std::sync::Arc;

    #[tokio::test]
    async fn test_retry_success_on_first_attempt() {
        let config = RetryConfig {
            max_attempts: 3,
            base_delay: Duration::from_millis(10),
            max_delay: Duration::from_millis(100),
            backoff_multiplier: 2.0,
            jitter: false,
        };

        let result = retry_with_backoff(&config, || async { Ok::<i32, anyhow::Error>(42) }).await;
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 42);
    }

    #[tokio::test]
    async fn test_retry_success_after_failures() {
        let config = RetryConfig {
            max_attempts: 3,
            base_delay: Duration::from_millis(10),
            max_delay: Duration::from_millis(100),
            backoff_multiplier: 2.0,
            jitter: false,
        };

        let attempt_count = Arc::new(AtomicU32::new(0));
        let attempt_count_clone = attempt_count.clone();

        let result = retry_with_backoff(&config, move || {
            let count = attempt_count_clone.fetch_add(1, Ordering::SeqCst) + 1;
            async move {
                if count < 3 {
                    Err(anyhow::anyhow!("network error"))
                } else {
                    Ok::<i32, anyhow::Error>(42)
                }
            }
        }).await;

        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 42);
        assert_eq!(attempt_count.load(Ordering::SeqCst), 3);
    }

    #[tokio::test]
    async fn test_retry_non_retryable_error() {
        let config = RetryConfig {
            max_attempts: 3,
            base_delay: Duration::from_millis(10),
            max_delay: Duration::from_millis(100),
            backoff_multiplier: 2.0,
            jitter: false,
        };

        let attempt_count = Arc::new(AtomicU32::new(0));
        let attempt_count_clone = attempt_count.clone();

        let result = retry_with_backoff(&config, move || {
            attempt_count_clone.fetch_add(1, Ordering::SeqCst);
            async move { Err::<i32, anyhow::Error>(anyhow::anyhow!("unauthorized access")) }
        }).await;

        assert!(result.is_err());
        // Should only attempt once for non-retryable errors
        assert_eq!(attempt_count.load(Ordering::SeqCst), 1);
    }

    #[tokio::test]
    async fn test_retry_max_attempts_exceeded() {
        let config = RetryConfig {
            max_attempts: 2,
            base_delay: Duration::from_millis(10),
            max_delay: Duration::from_millis(100),
            backoff_multiplier: 2.0,
            jitter: false,
        };

        let attempt_count = Arc::new(AtomicU32::new(0));
        let attempt_count_clone = attempt_count.clone();

        let result = retry_with_backoff(&config, move || {
            attempt_count_clone.fetch_add(1, Ordering::SeqCst);
            async move { Err::<i32, anyhow::Error>(anyhow::anyhow!("network error")) }
        }).await;

        assert!(result.is_err());
        assert_eq!(attempt_count.load(Ordering::SeqCst), 2);
    }
}
