//! Priority Queue System for Arbitrage Opportunities
//! 
//! This module implements a sophisticated priority queue that ranks arbitrage opportunities
//! based on multiple factors: profit potential, gas efficiency, success probability, and liquidity.

use std::collections::BinaryHeap;
use std::cmp::Ordering;
use std::time::{Duration, Instant};
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use anyhow::Result;

/// Represents a detected arbitrage opportunity with all relevant data
#[derive(Clone, Debug)]
pub struct ArbitrageOpportunity {
    pub id: String,
    pub token_mint: Pubkey,
    pub strategy_type: ArbitrageStrategy,
    pub buy_dex: String,
    pub sell_dex: String,
    pub buy_price: f64,
    pub sell_price: f64,
    pub price_difference_pct: f64,
    pub estimated_profit_sol: f64,
    pub estimated_profit_pct: f64,
    pub liquidity_available: u64,
    pub detected_at: Instant,
    pub expires_at: Instant,
    pub trade_amount: u64,
    pub estimated_gas_cost: u64,
    pub route_complexity: u8, // Number of hops (1 for direct, 2 for two-hop, 3 for triangle)
}

/// Different types of arbitrage strategies
#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum ArbitrageStrategy {
    /// Direct arbitrage between two DEXes
    MultiDex,
    /// Two-hop arbitrage: Token A -> Token B -> Token A
    TwoHop { intermediate_token: Pubkey },
    /// Triangle arbitrage: Token A -> Token B -> Token C -> Token A
    Triangle { token_b: Pubkey, token_c: Pubkey },
}

/// Prioritized opportunity with calculated scores
#[derive(Clone, Debug)]
pub struct PrioritizedOpportunity {
    pub opportunity: ArbitrageOpportunity,
    pub profit_score: f64,
    pub gas_efficiency_score: f64,
    pub success_probability_score: f64,
    pub liquidity_score: f64,
    pub time_decay_score: f64,
    pub composite_score: f64,
    pub priority_rank: u64,
}

impl PartialEq for PrioritizedOpportunity {
    fn eq(&self, other: &Self) -> bool {
        self.composite_score == other.composite_score
    }
}

impl Eq for PrioritizedOpportunity {}

impl PartialOrd for PrioritizedOpportunity {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for PrioritizedOpportunity {
    fn cmp(&self, other: &Self) -> Ordering {
        // Higher composite score = higher priority
        self.composite_score.partial_cmp(&other.composite_score)
            .unwrap_or(Ordering::Equal)
            .then_with(|| self.priority_rank.cmp(&other.priority_rank))
    }
}

/// Configuration for the priority queue scoring system
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct PriorityQueueConfig {
    pub profit_weight: f64,
    pub gas_efficiency_weight: f64,
    pub success_probability_weight: f64,
    pub liquidity_weight: f64,
    pub time_decay_weight: f64,
    pub max_queue_size: usize,
    pub opportunity_ttl_seconds: u64,
    pub min_profit_threshold_bps: u64,
    pub max_gas_cost_sol: f64,
    pub min_liquidity_threshold: u64,
}

impl Default for PriorityQueueConfig {
    fn default() -> Self {
        Self {
            profit_weight: 0.35,           // 35% weight on profit
            gas_efficiency_weight: 0.25,   // 25% weight on gas efficiency
            success_probability_weight: 0.20, // 20% weight on success probability
            liquidity_weight: 0.15,        // 15% weight on liquidity
            time_decay_weight: 0.05,       // 5% weight on time decay
            max_queue_size: 100,
            opportunity_ttl_seconds: 30,   // Opportunities expire after 30 seconds
            min_profit_threshold_bps: 50,  // 0.5% minimum profit
            max_gas_cost_sol: 0.01,        // Maximum 0.01 SOL gas cost
            min_liquidity_threshold: 1000, // Minimum liquidity in lamports
        }
    }
}

/// Historical performance data for scoring
#[derive(Clone, Debug)]
pub struct PerformanceMetrics {
    pub dex_success_rates: std::collections::HashMap<String, f64>,
    pub strategy_success_rates: std::collections::HashMap<ArbitrageStrategy, f64>,
    pub average_gas_costs: std::collections::HashMap<ArbitrageStrategy, u64>,
    pub average_execution_times: std::collections::HashMap<String, Duration>,
    pub total_trades: u64,
    pub successful_trades: u64,
    pub total_profit_sol: f64,
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self {
            dex_success_rates: std::collections::HashMap::new(),
            strategy_success_rates: std::collections::HashMap::new(),
            average_gas_costs: std::collections::HashMap::new(),
            average_execution_times: std::collections::HashMap::new(),
            total_trades: 0,
            successful_trades: 0,
            total_profit_sol: 0.0,
        }
    }
}

/// Main priority queue for arbitrage opportunities
pub struct OpportunityQueue {
    queue: Arc<RwLock<BinaryHeap<PrioritizedOpportunity>>>,
    config: PriorityQueueConfig,
    performance_metrics: Arc<RwLock<PerformanceMetrics>>,
    next_priority_rank: Arc<std::sync::atomic::AtomicU64>,
}

impl OpportunityQueue {
    /// Create a new priority queue with default configuration
    pub fn new() -> Self {
        Self::with_config(PriorityQueueConfig::default())
    }

    /// Create a new priority queue with custom configuration
    pub fn with_config(config: PriorityQueueConfig) -> Self {
        Self {
            queue: Arc::new(RwLock::new(BinaryHeap::new())),
            config,
            performance_metrics: Arc::new(RwLock::new(PerformanceMetrics::default())),
            next_priority_rank: Arc::new(std::sync::atomic::AtomicU64::new(0)),
        }
    }

    /// Add a new arbitrage opportunity to the queue
    pub async fn add_opportunity(&self, opportunity: ArbitrageOpportunity) -> Result<()> {
        // Validate opportunity meets minimum thresholds
        if !self.validate_opportunity(&opportunity)? {
            return Ok(()); // Skip invalid opportunities
        }

        // Calculate priority scores
        let prioritized = self.calculate_priority_scores(opportunity).await?;

        let mut queue = self.queue.write().await;
        
        // Add to queue
        queue.push(prioritized);

        // Enforce max queue size by removing lowest priority items
        while queue.len() > self.config.max_queue_size {
            queue.pop(); // Remove lowest priority (BinaryHeap is max-heap)
        }

        Ok(())
    }

    /// Get the highest priority opportunity from the queue
    pub async fn get_next_opportunity(&self) -> Option<PrioritizedOpportunity> {
        let mut queue = self.queue.write().await;
        
        // Remove expired opportunities
        self.cleanup_expired_opportunities(&mut queue).await;
        
        queue.pop()
    }

    /// Peek at the highest priority opportunity without removing it
    pub async fn peek_next_opportunity(&self) -> Option<PrioritizedOpportunity> {
        let queue = self.queue.read().await;
        queue.peek().cloned()
    }

    /// Get current queue size
    pub async fn size(&self) -> usize {
        let queue = self.queue.read().await;
        queue.len()
    }

    /// Clear all opportunities from the queue
    pub async fn clear(&self) {
        let mut queue = self.queue.write().await;
        queue.clear();
    }

    /// Get all opportunities currently in the queue (for monitoring/debugging)
    pub async fn get_all_opportunities(&self) -> Vec<PrioritizedOpportunity> {
        let queue = self.queue.read().await;
        queue.iter().cloned().collect()
    }

    /// Update performance metrics after trade execution
    pub async fn update_performance_metrics(
        &self,
        opportunity: &ArbitrageOpportunity,
        success: bool,
        actual_profit_sol: f64,
        actual_gas_cost: u64,
        execution_time: Duration,
    ) -> Result<()> {
        let mut metrics = self.performance_metrics.write().await;
        
        metrics.total_trades += 1;
        if success {
            metrics.successful_trades += 1;
            metrics.total_profit_sol += actual_profit_sol;
        }

        // Update DEX success rates
        let dex_key = format!("{}_{}", opportunity.buy_dex, opportunity.sell_dex);
        let current_rate = metrics.dex_success_rates.get(&dex_key).unwrap_or(&0.0);
        let new_rate = if success {
            (current_rate * 0.9) + 0.1 // Exponential moving average
        } else {
            current_rate * 0.9
        };
        metrics.dex_success_rates.insert(dex_key, new_rate);

        // Update strategy success rates
        let strategy_rate = metrics.strategy_success_rates.get(&opportunity.strategy_type).unwrap_or(&0.0);
        let new_strategy_rate = if success {
            (strategy_rate * 0.9) + 0.1
        } else {
            strategy_rate * 0.9
        };
        metrics.strategy_success_rates.insert(opportunity.strategy_type.clone(), new_strategy_rate);

        // Update average gas costs
        let current_gas = metrics.average_gas_costs.get(&opportunity.strategy_type).unwrap_or(&0);
        let new_gas = ((*current_gas as f64 * 0.9) + (actual_gas_cost as f64 * 0.1)) as u64;
        metrics.average_gas_costs.insert(opportunity.strategy_type.clone(), new_gas);

        // Update execution times
        let execution_key = format!("{}_{}", opportunity.buy_dex, opportunity.sell_dex);
        metrics.average_execution_times.insert(execution_key, execution_time);

        Ok(())
    }

    /// Validate that an opportunity meets minimum thresholds
    fn validate_opportunity(&self, opportunity: &ArbitrageOpportunity) -> Result<bool> {
        // Check minimum profit threshold
        let profit_bps = (opportunity.estimated_profit_pct * 100.0) as u64;
        if profit_bps < self.config.min_profit_threshold_bps {
            return Ok(false);
        }

        // Check maximum gas cost
        let gas_cost_sol = opportunity.estimated_gas_cost as f64 / 1_000_000_000.0;
        if gas_cost_sol > self.config.max_gas_cost_sol {
            return Ok(false);
        }

        // Check minimum liquidity
        if opportunity.liquidity_available < self.config.min_liquidity_threshold {
            return Ok(false);
        }

        // Check if opportunity has expired
        if Instant::now() > opportunity.expires_at {
            return Ok(false);
        }

        Ok(true)
    }

    /// Calculate priority scores for an opportunity
    async fn calculate_priority_scores(&self, opportunity: ArbitrageOpportunity) -> Result<PrioritizedOpportunity> {
        let metrics = self.performance_metrics.read().await;

        // Calculate individual scores (0.0 to 1.0 scale)
        let profit_score = self.calculate_profit_score(&opportunity);
        let gas_efficiency_score = self.calculate_gas_efficiency_score(&opportunity);
        let success_probability_score = self.calculate_success_probability_score(&opportunity, &metrics);
        let liquidity_score = self.calculate_liquidity_score(&opportunity);
        let time_decay_score = self.calculate_time_decay_score(&opportunity);

        // Calculate weighted composite score
        let composite_score =
            (profit_score * self.config.profit_weight) +
            (gas_efficiency_score * self.config.gas_efficiency_weight) +
            (success_probability_score * self.config.success_probability_weight) +
            (liquidity_score * self.config.liquidity_weight) +
            (time_decay_score * self.config.time_decay_weight);

        let priority_rank = self.next_priority_rank.fetch_add(1, std::sync::atomic::Ordering::SeqCst);

        Ok(PrioritizedOpportunity {
            opportunity,
            profit_score,
            gas_efficiency_score,
            success_probability_score,
            liquidity_score,
            time_decay_score,
            composite_score,
            priority_rank,
        })
    }

    /// Calculate profit score (0.0 to 1.0)
    fn calculate_profit_score(&self, opportunity: &ArbitrageOpportunity) -> f64 {
        // Normalize profit percentage to 0-1 scale
        // Assume max expected profit is 10% for normalization
        let max_profit_pct = 10.0;
        let normalized_profit = (opportunity.estimated_profit_pct / max_profit_pct).min(1.0);

        // Apply logarithmic scaling to favor higher profits more
        if normalized_profit > 0.0 {
            (normalized_profit.ln() + 1.0).max(0.0).min(1.0)
        } else {
            0.0
        }
    }

    /// Calculate gas efficiency score (0.0 to 1.0)
    fn calculate_gas_efficiency_score(&self, opportunity: &ArbitrageOpportunity) -> f64 {
        let gas_cost_sol = opportunity.estimated_gas_cost as f64 / 1_000_000_000.0;
        let profit_to_gas_ratio = opportunity.estimated_profit_sol / gas_cost_sol.max(0.001);

        // Normalize ratio - assume max good ratio is 50:1
        let max_ratio = 50.0;
        (profit_to_gas_ratio / max_ratio).min(1.0)
    }

    /// Calculate success probability score based on historical data (0.0 to 1.0)
    fn calculate_success_probability_score(&self, opportunity: &ArbitrageOpportunity, metrics: &PerformanceMetrics) -> f64 {
        let mut score = 0.5; // Base score

        // Factor in DEX pair success rate
        let dex_key = format!("{}_{}", opportunity.buy_dex, opportunity.sell_dex);
        if let Some(dex_rate) = metrics.dex_success_rates.get(&dex_key) {
            score = (score + dex_rate) / 2.0;
        }

        // Factor in strategy success rate
        if let Some(strategy_rate) = metrics.strategy_success_rates.get(&opportunity.strategy_type) {
            score = (score + strategy_rate) / 2.0;
        }

        // Penalize complex routes
        match opportunity.route_complexity {
            1 => score, // No penalty for direct arbitrage
            2 => score * 0.9, // 10% penalty for two-hop
            3 => score * 0.8, // 20% penalty for triangle
            _ => score * 0.7, // 30% penalty for more complex routes
        }
    }

    /// Calculate liquidity score (0.0 to 1.0)
    fn calculate_liquidity_score(&self, opportunity: &ArbitrageOpportunity) -> f64 {
        // Normalize liquidity - assume 1 SOL (1B lamports) is good liquidity
        let good_liquidity = 1_000_000_000_u64;
        let liquidity_ratio = opportunity.liquidity_available as f64 / good_liquidity as f64;

        // Use square root to diminish returns on very high liquidity
        liquidity_ratio.sqrt().min(1.0)
    }

    /// Calculate time decay score - newer opportunities score higher (0.0 to 1.0)
    fn calculate_time_decay_score(&self, opportunity: &ArbitrageOpportunity) -> f64 {
        let age = Instant::now().duration_since(opportunity.detected_at);
        let max_age = Duration::from_secs(self.config.opportunity_ttl_seconds);

        if age >= max_age {
            return 0.0;
        }

        let age_ratio = age.as_secs_f64() / max_age.as_secs_f64();
        1.0 - age_ratio
    }

    /// Remove expired opportunities from the queue
    async fn cleanup_expired_opportunities(&self, queue: &mut BinaryHeap<PrioritizedOpportunity>) {
        let now = Instant::now();
        let mut temp_vec: Vec<PrioritizedOpportunity> = Vec::new();

        // Drain all items and keep only non-expired ones
        while let Some(opportunity) = queue.pop() {
            if now <= opportunity.opportunity.expires_at {
                temp_vec.push(opportunity);
            }
        }

        // Put non-expired opportunities back
        for opportunity in temp_vec {
            queue.push(opportunity);
        }
    }

    /// Get performance metrics for monitoring
    pub async fn get_performance_metrics(&self) -> PerformanceMetrics {
        let metrics = self.performance_metrics.read().await;
        metrics.clone()
    }

    /// Get queue statistics for monitoring
    pub async fn get_queue_stats(&self) -> QueueStats {
        let queue = self.queue.read().await;
        let opportunities: Vec<_> = queue.iter().collect();

        let total_count = opportunities.len();
        let avg_composite_score = if total_count > 0 {
            opportunities.iter().map(|o| o.composite_score).sum::<f64>() / total_count as f64
        } else {
            0.0
        };

        let strategy_distribution = {
            let mut dist = std::collections::HashMap::new();
            for opp in &opportunities {
                *dist.entry(opp.opportunity.strategy_type.clone()).or_insert(0) += 1;
            }
            dist
        };

        QueueStats {
            total_opportunities: total_count,
            average_composite_score: avg_composite_score,
            strategy_distribution,
            oldest_opportunity_age: opportunities.iter()
                .map(|o| Instant::now().duration_since(o.opportunity.detected_at))
                .max(),
        }
    }
}

/// Statistics about the current state of the queue
#[derive(Clone, Debug)]
pub struct QueueStats {
    pub total_opportunities: usize,
    pub average_composite_score: f64,
    pub strategy_distribution: std::collections::HashMap<ArbitrageStrategy, usize>,
    pub oldest_opportunity_age: Option<Duration>,
}
