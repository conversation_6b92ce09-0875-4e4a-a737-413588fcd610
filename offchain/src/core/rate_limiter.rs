use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Semaphore;
use anyhow::Result;
use std::future::Future;
use prometheus::{Counter, Histogram, register_counter, register_histogram};
use once_cell::sync::Lazy;

// Prometheus metrics for rate limiting
static RPC_CALLS_TOTAL: Lazy<Counter> = Lazy::new(|| {
    register_counter!("rpc_calls_total", "Total number of RPC calls made").unwrap()
});

static RPC_CALL_DURATION: Lazy<Histogram> = Lazy::new(|| {
    register_histogram!("rpc_call_duration_seconds", "Duration of RPC calls").unwrap()
});

static SWAP_OPERATIONS_TOTAL: Lazy<Counter> = Lazy::new(|| {
    register_counter!("swap_operations_total", "Total number of swap operations").unwrap()
});

static PRICE_CHECKS_TOTAL: Lazy<Counter> = Lazy::new(|| {
    register_counter!("price_checks_total", "Total number of price checks").unwrap()
});

/// Configuration for rate limiting different types of operations
#[derive(Debug, <PERSON>lone)]
pub struct RateLimiterConfig {
    pub max_concurrent_rpc_calls: usize,
    pub max_concurrent_swaps: usize,
    pub max_concurrent_price_checks: usize,
    pub max_concurrent_pool_discovery: usize,
}

impl Default for RateLimiterConfig {
    fn default() -> Self {
        Self {
            max_concurrent_rpc_calls: 15,
            max_concurrent_swaps: 3,
            max_concurrent_price_checks: 25,
            max_concurrent_pool_discovery: 8,
        }
    }
}

impl RateLimiterConfig {
    /// Load configuration from environment variables
    pub fn from_env() -> Self {
        Self {
            max_concurrent_rpc_calls: std::env::var("MAX_CONCURRENT_RPC_CALLS")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(15),
            max_concurrent_swaps: std::env::var("MAX_CONCURRENT_SWAPS")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(3),
            max_concurrent_price_checks: std::env::var("MAX_CONCURRENT_PRICE_CHECKS")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(25),
            max_concurrent_pool_discovery: std::env::var("MAX_CONCURRENT_POOL_DISCOVERY")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(8),
        }
    }
}

/// Rate limiter using semaphores to control concurrent operations
#[derive(Debug, Clone)]
pub struct RateLimiter {
    rpc_semaphore: Arc<Semaphore>,
    swap_semaphore: Arc<Semaphore>,
    price_check_semaphore: Arc<Semaphore>,
    pool_discovery_semaphore: Arc<Semaphore>,
}

impl RateLimiter {
    /// Create a new rate limiter with the given configuration
    pub fn new(config: RateLimiterConfig) -> Self {
        Self {
            rpc_semaphore: Arc::new(Semaphore::new(config.max_concurrent_rpc_calls)),
            swap_semaphore: Arc::new(Semaphore::new(config.max_concurrent_swaps)),
            price_check_semaphore: Arc::new(Semaphore::new(config.max_concurrent_price_checks)),
            pool_discovery_semaphore: Arc::new(Semaphore::new(config.max_concurrent_pool_discovery)),
        }
    }

    /// Execute an RPC operation with rate limiting
    pub async fn execute_rpc<F, Fut, T>(&self, operation: F) -> Result<T>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = Result<T>>,
    {
        let _permit = self.rpc_semaphore.acquire().await
            .map_err(|_| anyhow::anyhow!("Failed to acquire RPC semaphore permit"))?;
        
        RPC_CALLS_TOTAL.inc();
        let timer = RPC_CALL_DURATION.start_timer();
        
        let result = operation().await;
        timer.observe_duration();
        
        result
    }

    /// Execute a swap operation with rate limiting
    pub async fn execute_swap<F, Fut, T>(&self, operation: F) -> Result<T>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = Result<T>>,
    {
        let _permit = self.swap_semaphore.acquire().await
            .map_err(|_| anyhow::anyhow!("Failed to acquire swap semaphore permit"))?;
        
        SWAP_OPERATIONS_TOTAL.inc();
        operation().await
    }

    /// Execute a price check operation with rate limiting
    pub async fn execute_price_check<F, Fut, T>(&self, operation: F) -> Result<T>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = Result<T>>,
    {
        let _permit = self.price_check_semaphore.acquire().await
            .map_err(|_| anyhow::anyhow!("Failed to acquire price check semaphore permit"))?;
        
        PRICE_CHECKS_TOTAL.inc();
        operation().await
    }

    /// Execute a pool discovery operation with rate limiting
    pub async fn execute_pool_discovery<F, Fut, T>(&self, operation: F) -> Result<T>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = Result<T>>,
    {
        let _permit = self.pool_discovery_semaphore.acquire().await
            .map_err(|_| anyhow::anyhow!("Failed to acquire pool discovery semaphore permit"))?;
        
        operation().await
    }

    /// Get current available permits for monitoring
    pub fn get_available_permits(&self) -> RateLimiterStatus {
        RateLimiterStatus {
            rpc_available: self.rpc_semaphore.available_permits(),
            swap_available: self.swap_semaphore.available_permits(),
            price_check_available: self.price_check_semaphore.available_permits(),
            pool_discovery_available: self.pool_discovery_semaphore.available_permits(),
        }
    }

    /// Try to execute an RPC operation without waiting (non-blocking)
    pub async fn try_execute_rpc<F, Fut, T>(&self, operation: F) -> Result<Option<T>>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = Result<T>>,
    {
        match self.rpc_semaphore.try_acquire() {
            Ok(_permit) => {
                RPC_CALLS_TOTAL.inc();
                let timer = RPC_CALL_DURATION.start_timer();
                let result = operation().await;
                timer.observe_duration();
                Ok(Some(result?))
            }
            Err(_) => Ok(None), // No permits available
        }
    }

    /// Execute with timeout to prevent hanging operations
    pub async fn execute_rpc_with_timeout<F, Fut, T>(
        &self,
        operation: F,
        timeout: Duration,
    ) -> Result<T>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = Result<T>>,
    {
        let _permit = tokio::time::timeout(timeout, self.rpc_semaphore.acquire())
            .await
            .map_err(|_| anyhow::anyhow!("Timeout waiting for RPC semaphore permit"))?
            .map_err(|_| anyhow::anyhow!("Failed to acquire RPC semaphore permit"))?;
        
        RPC_CALLS_TOTAL.inc();
        let timer = RPC_CALL_DURATION.start_timer();
        
        let result = tokio::time::timeout(timeout, operation())
            .await
            .map_err(|_| anyhow::anyhow!("RPC operation timed out"))?;
        
        timer.observe_duration();
        result
    }
}

/// Status information about current rate limiter state
#[derive(Debug, Clone)]
pub struct RateLimiterStatus {
    pub rpc_available: usize,
    pub swap_available: usize,
    pub price_check_available: usize,
    pub pool_discovery_available: usize,
}

impl RateLimiterStatus {
    /// Check if the rate limiter is under pressure (low available permits)
    pub fn is_under_pressure(&self) -> bool {
        self.rpc_available <= 2 || 
        self.swap_available == 0 || 
        self.price_check_available <= 5
    }

    /// Get a human-readable status string
    pub fn status_string(&self) -> String {
        format!(
            "RPC: {}, Swap: {}, Price: {}, Pool: {}",
            self.rpc_available,
            self.swap_available,
            self.price_check_available,
            self.pool_discovery_available
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{sleep, Duration};

    #[tokio::test]
    async fn test_rate_limiter_basic() {
        let config = RateLimiterConfig {
            max_concurrent_rpc_calls: 2,
            max_concurrent_swaps: 1,
            max_concurrent_price_checks: 3,
            max_concurrent_pool_discovery: 1,
        };
        
        let rate_limiter = RateLimiter::new(config);
        
        // Test that we can execute operations
        let result = rate_limiter.execute_rpc(|| async { Ok::<i32, anyhow::Error>(42) }).await;
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 42);
    }

    #[tokio::test]
    async fn test_rate_limiter_concurrency() {
        let config = RateLimiterConfig {
            max_concurrent_rpc_calls: 1,
            max_concurrent_swaps: 1,
            max_concurrent_price_checks: 1,
            max_concurrent_pool_discovery: 1,
        };
        
        let rate_limiter = Arc::new(RateLimiter::new(config));
        
        // Start a long-running operation
        let rate_limiter_clone = rate_limiter.clone();
        let handle1 = tokio::spawn(async move {
            rate_limiter_clone.execute_rpc(|| async {
                sleep(Duration::from_millis(100)).await;
                Ok::<i32, anyhow::Error>(1)
            }).await
        });
        
        // Try to execute another operation immediately (should wait)
        let rate_limiter_clone = rate_limiter.clone();
        let handle2 = tokio::spawn(async move {
            rate_limiter_clone.execute_rpc(|| async { Ok::<i32, anyhow::Error>(2) }).await
        });
        
        let results = tokio::join!(handle1, handle2);
        assert!(results.0.is_ok());
        assert!(results.1.is_ok());
    }
}
