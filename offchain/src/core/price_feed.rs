//! Enhanced Multi-Source Price Feed Integration
//! 
//! This module provides a comprehensive price feed system that integrates multiple data sources
//! including Birdeye API, Yellowstone gRPC, and direct DEX pool monitoring with fallback
//! mechanisms, data validation, and real-time updates.

use std::{
    collections::HashMap,
    sync::Arc,
    time::{Duration, SystemTime, UNIX_EPOCH},
    str::FromStr,
};
use anyhow::{Result, anyhow};
use tokio::{
    sync::{RwLock, mpsc},
    time::{interval, Instant},
};
use serde::{Deserialize, Serialize};
use anchor_client::solana_sdk::pubkey::Pubkey;
use colored::Colorize;

use crate::core::{
    birdeye_api::{BirdeyeClient, BirdeyeTokenPrice},
    app_state::EnhancedAppState,
};

/// Price data source types
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum PriceSource {
    BirdeyeAPI,
    YellowstoneGRPC,
    DirectDEX,
    Jupiter,
}

/// Price data with metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceData {
    pub price: f64,
    pub timestamp: u64,
    pub source: PriceSource,
    pub confidence: f64, // 0.0 to 1.0
    pub volume_24h: Option<f64>,
    pub liquidity: Option<f64>,
    pub price_change_24h: Option<f64>,
}

/// Aggregated price information from multiple sources
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AggregatedPrice {
    pub weighted_price: f64,
    pub confidence_score: f64,
    pub sources: Vec<PriceData>,
    pub last_updated: u64,
    pub price_deviation: f64, // Standard deviation across sources
}

/// Price feed configuration
#[derive(Debug, Clone)]
pub struct PriceFeedConfig {
    pub update_interval_ms: u64,
    pub max_price_age_ms: u64,
    pub min_confidence_threshold: f64,
    pub max_price_deviation_pct: f64,
    pub enable_birdeye: bool,
    pub enable_yellowstone: bool,
    pub enable_direct_dex: bool,
    pub fallback_timeout_ms: u64,
}

impl Default for PriceFeedConfig {
    fn default() -> Self {
        Self {
            update_interval_ms: 1000, // 1 second
            max_price_age_ms: 30000,  // 30 seconds
            min_confidence_threshold: 0.7,
            max_price_deviation_pct: 5.0, // 5%
            enable_birdeye: true,
            enable_yellowstone: true,
            enable_direct_dex: true,
            fallback_timeout_ms: 5000, // 5 seconds
        }
    }
}

/// Multi-source price feed manager
pub struct PriceFeedManager {
    config: PriceFeedConfig,
    price_cache: Arc<RwLock<HashMap<String, AggregatedPrice>>>,
    birdeye_client: Option<BirdeyeClient>,
    app_state: Arc<EnhancedAppState>,
    update_sender: mpsc::UnboundedSender<PriceUpdateMessage>,
    update_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<PriceUpdateMessage>>>>,
}

/// Internal message for price updates
#[derive(Debug, Clone)]
struct PriceUpdateMessage {
    token_address: String,
    price_data: PriceData,
}

impl PriceFeedManager {
    /// Create a new price feed manager
    pub async fn new(
        config: PriceFeedConfig,
        app_state: Arc<EnhancedAppState>,
    ) -> Result<Self> {
        let birdeye_client = if config.enable_birdeye {
            match BirdeyeClient::new() {
                Ok(client) => Some(client),
                Err(e) => {
                    println!("⚠️  Warning: Failed to initialize Birdeye client: {}", e);
                    None
                }
            }
        } else {
            None
        };

        let (update_sender, update_receiver) = mpsc::unbounded_channel();

        Ok(Self {
            config,
            price_cache: Arc::new(RwLock::new(HashMap::new())),
            birdeye_client,
            app_state,
            update_sender,
            update_receiver: Arc::new(RwLock::new(Some(update_receiver))),
        })
    }

    /// Start the price feed manager
    pub async fn start(&self) -> Result<()> {
        println!("{}", "🚀 Starting Multi-Source Price Feed Manager".green().bold());
        println!("{}", "═".repeat(60).green());

        // Start price update processor
        self.start_price_processor().await?;

        // Start periodic price fetching
        self.start_periodic_updates().await?;

        // Start real-time stream monitoring (if enabled)
        if self.config.enable_yellowstone {
            self.start_yellowstone_monitoring().await?;
        }

        println!("✅ Price Feed Manager started successfully");
        Ok(())
    }

    /// Get aggregated price for a token
    pub async fn get_price(&self, token_address: &str) -> Result<Option<AggregatedPrice>> {
        let cache = self.price_cache.read().await;
        
        if let Some(price) = cache.get(token_address) {
            let now = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64;
            
            // Check if price is still fresh
            if now - price.last_updated < self.config.max_price_age_ms {
                return Ok(Some(price.clone()));
            }
        }
        
        // If no fresh price in cache, fetch new price
        drop(cache);
        self.fetch_fresh_price(token_address).await
    }

    /// Fetch fresh price from all available sources
    async fn fetch_fresh_price(&self, token_address: &str) -> Result<Option<AggregatedPrice>> {
        let mut price_sources = Vec::new();

        // Fetch from Birdeye API
        if let Some(ref birdeye_client) = self.birdeye_client {
            if let Ok(birdeye_price) = birdeye_client.get_token_price(token_address).await {
                price_sources.push(PriceData {
                    price: birdeye_price.price,
                    timestamp: birdeye_price.timestamp as u64,
                    source: PriceSource::BirdeyeAPI,
                    confidence: 0.9, // High confidence for Birdeye
                    volume_24h: birdeye_price.volume24h,
                    liquidity: birdeye_price.liquidity,
                    price_change_24h: birdeye_price.price_change_24h,
                });
            }
        }

        // Fetch from Yellowstone gRPC (real-time DEX data)
        if self.config.enable_yellowstone {
            if let Ok(yellowstone_price) = self.fetch_yellowstone_price(token_address).await {
                price_sources.push(yellowstone_price);
            }
        }

        // Fetch from direct DEX pool monitoring
        if self.config.enable_direct_dex {
            if let Ok(dex_prices) = self.fetch_direct_dex_prices(token_address).await {
                price_sources.extend(dex_prices);
            }
        }

        if price_sources.is_empty() {
            return Ok(None);
        }

        // Aggregate prices
        let aggregated = self.aggregate_prices(price_sources)?;
        
        // Update cache
        let mut cache = self.price_cache.write().await;
        cache.insert(token_address.to_string(), aggregated.clone());
        
        Ok(Some(aggregated))
    }

    /// Aggregate prices from multiple sources
    fn aggregate_prices(&self, sources: Vec<PriceData>) -> Result<AggregatedPrice> {
        if sources.is_empty() {
            return Err(anyhow!("No price sources available"));
        }

        let total_confidence: f64 = sources.iter().map(|s| s.confidence).sum();
        let weighted_price: f64 = sources.iter()
            .map(|s| s.price * s.confidence)
            .sum::<f64>() / total_confidence;

        // Calculate price deviation
        let mean_price = sources.iter().map(|s| s.price).sum::<f64>() / sources.len() as f64;
        let variance = sources.iter()
            .map(|s| (s.price - mean_price).powi(2))
            .sum::<f64>() / sources.len() as f64;
        let price_deviation = variance.sqrt();

        let confidence_score = total_confidence / sources.len() as f64;
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        Ok(AggregatedPrice {
            weighted_price,
            confidence_score,
            sources,
            last_updated: now,
            price_deviation,
        })
    }

    /// Start the price update processor
    async fn start_price_processor(&self) -> Result<()> {
        let mut receiver = self.update_receiver.write().await
            .take()
            .ok_or_else(|| anyhow!("Price processor already started"))?;

        let price_cache = self.price_cache.clone();
        let config = self.config.clone();

        tokio::spawn(async move {
            while let Some(update) = receiver.recv().await {
                // Process price update
                let mut cache = price_cache.write().await;
                
                // Get existing aggregated price or create new one
                let mut aggregated = cache.get(&update.token_address)
                    .cloned()
                    .unwrap_or_else(|| AggregatedPrice {
                        weighted_price: update.price_data.price,
                        confidence_score: update.price_data.confidence,
                        sources: vec![],
                        last_updated: update.price_data.timestamp,
                        price_deviation: 0.0,
                    });

                // Update sources list
                aggregated.sources.retain(|s| s.source != update.price_data.source);
                aggregated.sources.push(update.price_data);

                // Recalculate aggregated price
                if let Ok(new_aggregated) = Self::aggregate_prices_static(&aggregated.sources) {
                    cache.insert(update.token_address, new_aggregated);
                }
            }
        });

        Ok(())
    }

    /// Static version of aggregate_prices for use in spawned tasks
    fn aggregate_prices_static(sources: &[PriceData]) -> Result<AggregatedPrice> {
        if sources.is_empty() {
            return Err(anyhow!("No price sources available"));
        }

        let total_confidence: f64 = sources.iter().map(|s| s.confidence).sum();
        let weighted_price: f64 = sources.iter()
            .map(|s| s.price * s.confidence)
            .sum::<f64>() / total_confidence;

        let mean_price = sources.iter().map(|s| s.price).sum::<f64>() / sources.len() as f64;
        let variance = sources.iter()
            .map(|s| (s.price - mean_price).powi(2))
            .sum::<f64>() / sources.len() as f64;
        let price_deviation = variance.sqrt();

        let confidence_score = total_confidence / sources.len() as f64;
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        Ok(AggregatedPrice {
            weighted_price,
            confidence_score,
            sources: sources.to_vec(),
            last_updated: now,
            price_deviation,
        })
    }

    /// Start periodic price updates
    async fn start_periodic_updates(&self) -> Result<()> {
        let update_sender = self.update_sender.clone();
        let birdeye_client = self.birdeye_client.clone();
        let interval_ms = self.config.update_interval_ms;

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_millis(interval_ms));
            
            loop {
                interval.tick().await;
                
                // TODO: Implement periodic price fetching for tracked tokens
                // This would fetch prices for all tokens in our tracking list
            }
        });

        Ok(())
    }

    /// Fetch price from Yellowstone gRPC
    async fn fetch_yellowstone_price(&self, token_address: &str) -> Result<PriceData> {
        // Get latest price from Yellowstone gRPC stream
        // This would typically involve monitoring recent DEX transactions
        // For now, we'll use a placeholder implementation

        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        // TODO: Implement actual Yellowstone gRPC price extraction
        // This would analyze recent swap transactions to derive current prices
        Err(anyhow!("Yellowstone gRPC price fetching not yet implemented"))
    }

    /// Fetch prices from direct DEX pool monitoring
    async fn fetch_direct_dex_prices(&self, token_address: &str) -> Result<Vec<PriceData>> {
        let mut dex_prices = Vec::new();
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        // Parse token address
        let token_pubkey = match Pubkey::from_str(token_address) {
            Ok(pubkey) => pubkey,
            Err(_) => return Ok(dex_prices),
        };

        // Fetch from Raydium pools
        if let Ok(raydium_price) = self.fetch_raydium_pool_price(&token_pubkey).await {
            dex_prices.push(PriceData {
                price: raydium_price,
                timestamp: now,
                source: PriceSource::DirectDEX,
                confidence: 0.85,
                volume_24h: None,
                liquidity: None,
                price_change_24h: None,
            });
        }

        // Fetch from Orca pools
        if let Ok(orca_price) = self.fetch_orca_pool_price(&token_pubkey).await {
            dex_prices.push(PriceData {
                price: orca_price,
                timestamp: now,
                source: PriceSource::DirectDEX,
                confidence: 0.85,
                volume_24h: None,
                liquidity: None,
                price_change_24h: None,
            });
        }

        // Fetch from Meteora pools
        if let Ok(meteora_price) = self.fetch_meteora_pool_price(&token_pubkey).await {
            dex_prices.push(PriceData {
                price: meteora_price,
                timestamp: now,
                source: PriceSource::DirectDEX,
                confidence: 0.80,
                volume_24h: None,
                liquidity: None,
                price_change_24h: None,
            });
        }

        Ok(dex_prices)
    }

    /// Fetch price from Raydium pools
    async fn fetch_raydium_pool_price(&self, token_mint: &Pubkey) -> Result<f64> {
        // TODO: Implement direct Raydium pool price fetching
        // This would query Raydium AMM/CLMM/CPMM pools for current prices
        Err(anyhow!("Raydium pool price fetching not yet implemented"))
    }

    /// Fetch price from Orca pools
    async fn fetch_orca_pool_price(&self, token_mint: &Pubkey) -> Result<f64> {
        // TODO: Implement direct Orca pool price fetching
        // This would query Orca Whirlpool accounts for current prices
        Err(anyhow!("Orca pool price fetching not yet implemented"))
    }

    /// Fetch price from Meteora pools
    async fn fetch_meteora_pool_price(&self, token_mint: &Pubkey) -> Result<f64> {
        // TODO: Implement direct Meteora pool price fetching
        // This would query Meteora DLMM pools for current prices
        Err(anyhow!("Meteora pool price fetching not yet implemented"))
    }

    /// Start Yellowstone gRPC monitoring for real-time price updates
    async fn start_yellowstone_monitoring(&self) -> Result<()> {
        println!("🔄 Starting Yellowstone gRPC monitoring for real-time price updates");

        // TODO: Implement Yellowstone gRPC integration for real-time price updates
        // This would monitor DEX transactions and extract price information
        // For now, we'll set up the framework but not the actual implementation

        let update_sender = self.update_sender.clone();

        tokio::spawn(async move {
            // This would be the Yellowstone gRPC subscription loop
            // It would monitor swap transactions and extract price data
            println!("⚠️  Yellowstone gRPC monitoring framework ready (implementation pending)");

            // Placeholder for actual Yellowstone gRPC stream processing
            loop {
                tokio::time::sleep(Duration::from_secs(60)).await;
                // Process Yellowstone updates and send price updates via update_sender
            }
        });

        Ok(())
    }

    /// Get price statistics
    pub async fn get_price_stats(&self) -> HashMap<String, AggregatedPrice> {
        self.price_cache.read().await.clone()
    }

    /// Clear old price data
    pub async fn cleanup_old_prices(&self) {
        let mut cache = self.price_cache.write().await;
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        cache.retain(|_, price| {
            now - price.last_updated < self.config.max_price_age_ms * 2
        });
    }
}
