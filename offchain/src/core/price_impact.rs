//! Price Impact Calculation Engine
//! 
//! This module provides sophisticated price impact calculation that estimates slippage
//! for different trade sizes, considers pool liquidity across multiple DEXes, and
//! provides trade size recommendations to minimize price impact.

use std::{
    collections::HashMap,
    sync::Arc,
};
use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use anchor_client::solana_sdk::pubkey::Pubkey;
use colored::Colorize;

use crate::core::{
    pool_monitor::{PoolState, PoolType},
    market_depth::{MarketDepth, TradeDirection},
};

/// Price impact analysis result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceImpactAnalysis {
    pub token_pair: (Pubkey, Pubkey),
    pub trade_direction: TradeDirection,
    pub trade_amount: u64,
    pub estimated_impact: f64,
    pub impact_breakdown: Vec<PoolImpact>,
    pub optimal_split: Vec<TradeSplit>,
    pub total_slippage: f64,
    pub execution_price: f64,
    pub market_price: f64,
    pub confidence_score: f64,
}

/// Price impact for individual pool
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolImpact {
    pub pool_id: Pubkey,
    pub dex_name: String,
    pub pool_type: PoolType,
    pub current_price: f64,
    pub impact_percentage: f64,
    pub max_trade_size: u64,
    pub liquidity_utilization: f64,
    pub fee_impact: f64,
}

/// Optimal trade split across pools
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeSplit {
    pub pool_id: Pubkey,
    pub dex_name: String,
    pub allocation_amount: u64,
    pub allocation_percentage: f64,
    pub expected_output: u64,
    pub price_impact: f64,
    pub execution_order: u32,
}

/// Price impact calculation configuration
#[derive(Debug, Clone)]
pub struct PriceImpactConfig {
    pub max_acceptable_impact: f64,    // Maximum acceptable price impact (%)
    pub liquidity_utilization_limit: f64, // Max % of pool liquidity to use
    pub min_pool_liquidity: u64,       // Minimum pool liquidity to consider
    pub impact_calculation_method: ImpactCalculationMethod,
    pub slippage_tolerance: f64,       // Additional slippage tolerance (%)
    pub fee_consideration: bool,       // Whether to include fees in impact calculation
}

/// Methods for calculating price impact
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ImpactCalculationMethod {
    ConstantProduct,  // x * y = k formula
    StableSwap,      // Curve-style stable swap
    ConcentratedLiquidity, // Uniswap V3 style
    Hybrid,          // Adaptive based on pool type
}

impl Default for PriceImpactConfig {
    fn default() -> Self {
        Self {
            max_acceptable_impact: 3.0,      // 3%
            liquidity_utilization_limit: 0.1, // 10% of pool liquidity
            min_pool_liquidity: 10000,       // 10k tokens minimum
            impact_calculation_method: ImpactCalculationMethod::Hybrid,
            slippage_tolerance: 0.5,         // 0.5%
            fee_consideration: true,
        }
    }
}

/// Price impact calculator
pub struct PriceImpactCalculator {
    config: PriceImpactConfig,
}

impl PriceImpactCalculator {
    /// Create a new price impact calculator
    pub fn new(config: PriceImpactConfig) -> Self {
        Self { config }
    }

    /// Calculate price impact for a trade
    pub async fn calculate_impact(
        &self,
        pools: &[PoolState],
        trade_direction: &TradeDirection,
        trade_amount: u64,
        token_a: &Pubkey,
        token_b: &Pubkey,
    ) -> Result<PriceImpactAnalysis> {
        if pools.is_empty() {
            return Err(anyhow!("No pools available for price impact calculation"));
        }

        // Filter pools by minimum liquidity
        let viable_pools: Vec<&PoolState> = pools.iter()
            .filter(|pool| pool.liquidity >= self.config.min_pool_liquidity)
            .collect();

        if viable_pools.is_empty() {
            return Err(anyhow!("No pools meet minimum liquidity requirements"));
        }

        // Calculate impact for each pool
        let mut pool_impacts = Vec::new();
        for pool in &viable_pools {
            let impact = self.calculate_single_pool_impact(pool, trade_direction, trade_amount).await?;
            pool_impacts.push(impact);
        }

        // Calculate optimal trade split
        let optimal_split = self.calculate_optimal_split(&pool_impacts, trade_amount).await?;

        // Calculate overall metrics
        let estimated_impact = self.calculate_weighted_impact(&optimal_split);
        let total_slippage = estimated_impact + self.config.slippage_tolerance;
        let market_price = self.calculate_market_price(&viable_pools);
        let execution_price = market_price * (1.0 + estimated_impact / 100.0);
        let confidence_score = self.calculate_confidence_score(&pool_impacts);

        Ok(PriceImpactAnalysis {
            token_pair: (*token_a, *token_b),
            trade_direction: trade_direction.clone(),
            trade_amount,
            estimated_impact,
            impact_breakdown: pool_impacts,
            optimal_split,
            total_slippage,
            execution_price,
            market_price,
            confidence_score,
        })
    }

    /// Calculate price impact for a single pool
    async fn calculate_single_pool_impact(
        &self,
        pool: &PoolState,
        trade_direction: &TradeDirection,
        trade_amount: u64,
    ) -> Result<PoolImpact> {
        let impact_percentage = match self.config.impact_calculation_method {
            ImpactCalculationMethod::ConstantProduct => {
                self.calculate_constant_product_impact(pool, trade_direction, trade_amount)
            }
            ImpactCalculationMethod::StableSwap => {
                self.calculate_stable_swap_impact(pool, trade_direction, trade_amount)
            }
            ImpactCalculationMethod::ConcentratedLiquidity => {
                self.calculate_concentrated_liquidity_impact(pool, trade_direction, trade_amount)
            }
            ImpactCalculationMethod::Hybrid => {
                self.calculate_hybrid_impact(pool, trade_direction, trade_amount)
            }
        };

        let max_trade_size = self.calculate_max_trade_size(pool, trade_direction);
        let liquidity_utilization = trade_amount as f64 / max_trade_size as f64;
        let fee_impact = if self.config.fee_consideration {
            pool.fee_rate * 100.0
        } else {
            0.0
        };

        Ok(PoolImpact {
            pool_id: pool.pool_id,
            dex_name: pool.dex_name.clone(),
            pool_type: pool.pool_type.clone(),
            current_price: pool.current_price,
            impact_percentage,
            max_trade_size,
            liquidity_utilization,
            fee_impact,
        })
    }

    /// Calculate constant product AMM impact (x * y = k)
    fn calculate_constant_product_impact(
        &self,
        pool: &PoolState,
        trade_direction: &TradeDirection,
        trade_amount: u64,
    ) -> f64 {
        let (reserve_in, reserve_out) = match trade_direction {
            TradeDirection::AtoB => (pool.token_a_balance, pool.token_b_balance),
            TradeDirection::BtoA => (pool.token_b_balance, pool.token_a_balance),
        };

        if reserve_in == 0 || reserve_out == 0 {
            return 100.0; // 100% impact if no liquidity
        }

        // Constant product formula: (x + Δx) * (y - Δy) = x * y
        // Δy = (y * Δx) / (x + Δx)
        let amount_in = trade_amount as f64;
        let amount_out = (reserve_out as f64 * amount_in) / (reserve_in as f64 + amount_in);
        
        // Price impact = (amount_out / reserve_out) * 100
        let impact = (amount_out / reserve_out as f64) * 100.0;
        
        // Apply fee adjustment
        let fee_adjusted_impact = impact * (1.0 + pool.fee_rate);
        
        fee_adjusted_impact.min(100.0)
    }

    /// Calculate stable swap impact (for stable pairs)
    fn calculate_stable_swap_impact(
        &self,
        pool: &PoolState,
        trade_direction: &TradeDirection,
        trade_amount: u64,
    ) -> f64 {
        // Stable swap has lower impact for balanced trades
        let constant_product_impact = self.calculate_constant_product_impact(pool, trade_direction, trade_amount);
        
        // Stable swaps typically have 50-80% less impact than constant product
        let stable_factor = 0.3; // 70% reduction in impact
        constant_product_impact * stable_factor
    }

    /// Calculate concentrated liquidity impact (Uniswap V3 style)
    fn calculate_concentrated_liquidity_impact(
        &self,
        pool: &PoolState,
        trade_direction: &TradeDirection,
        trade_amount: u64,
    ) -> f64 {
        // Concentrated liquidity can have higher impact if trading outside the range
        let base_impact = self.calculate_constant_product_impact(pool, trade_direction, trade_amount);
        
        // Assume we're trading within the concentrated range (lower impact)
        // In reality, this would check the current price vs. the liquidity range
        let concentration_factor = 0.7; // 30% reduction due to concentration
        base_impact * concentration_factor
    }

    /// Calculate hybrid impact based on pool type
    fn calculate_hybrid_impact(
        &self,
        pool: &PoolState,
        trade_direction: &TradeDirection,
        trade_amount: u64,
    ) -> f64 {
        match pool.pool_type {
            PoolType::RaydiumAMM | PoolType::RaydiumCPMM => {
                self.calculate_constant_product_impact(pool, trade_direction, trade_amount)
            }
            PoolType::RaydiumCLMM | PoolType::OrcaWhirlpool => {
                self.calculate_concentrated_liquidity_impact(pool, trade_direction, trade_amount)
            }
            PoolType::MeteoraStable => {
                self.calculate_stable_swap_impact(pool, trade_direction, trade_amount)
            }
            PoolType::MeteoraDLMM => {
                // DLMM has unique characteristics, use concentrated liquidity as approximation
                self.calculate_concentrated_liquidity_impact(pool, trade_direction, trade_amount)
            }
            PoolType::PumpSwap => {
                // PumpSwap typically uses constant product
                self.calculate_constant_product_impact(pool, trade_direction, trade_amount)
            }
        }
    }

    /// Calculate maximum trade size for a pool
    fn calculate_max_trade_size(&self, pool: &PoolState, trade_direction: &TradeDirection) -> u64 {
        let available_liquidity = match trade_direction {
            TradeDirection::AtoB => pool.token_a_balance,
            TradeDirection::BtoA => pool.token_b_balance,
        };

        // Limit to configured percentage of pool liquidity
        (available_liquidity as f64 * self.config.liquidity_utilization_limit) as u64
    }

    /// Calculate optimal trade split across pools
    async fn calculate_optimal_split(
        &self,
        pool_impacts: &[PoolImpact],
        total_amount: u64,
    ) -> Result<Vec<TradeSplit>> {
        let mut splits = Vec::new();
        let mut remaining_amount = total_amount;

        // Sort pools by efficiency (lowest impact + fees)
        let mut sorted_pools: Vec<&PoolImpact> = pool_impacts.iter().collect();
        sorted_pools.sort_by(|a, b| {
            let efficiency_a = a.impact_percentage + a.fee_impact;
            let efficiency_b = b.impact_percentage + b.fee_impact;
            efficiency_a.partial_cmp(&efficiency_b).unwrap_or(std::cmp::Ordering::Equal)
        });

        let mut execution_order = 1u32;

        for pool_impact in sorted_pools {
            if remaining_amount == 0 {
                break;
            }

            // Calculate allocation for this pool
            let max_allocation = std::cmp::min(remaining_amount, pool_impact.max_trade_size);
            
            // Don't use pools with excessive impact
            if pool_impact.impact_percentage > self.config.max_acceptable_impact {
                continue;
            }

            if max_allocation > 0 {
                let allocation_percentage = (max_allocation as f64 / total_amount as f64) * 100.0;
                
                // Estimate output (simplified)
                let expected_output = (max_allocation as f64 * pool_impact.current_price * 
                    (1.0 - pool_impact.impact_percentage / 100.0)) as u64;

                splits.push(TradeSplit {
                    pool_id: pool_impact.pool_id,
                    dex_name: pool_impact.dex_name.clone(),
                    allocation_amount: max_allocation,
                    allocation_percentage,
                    expected_output,
                    price_impact: pool_impact.impact_percentage,
                    execution_order,
                });

                remaining_amount -= max_allocation;
                execution_order += 1;
            }
        }

        if remaining_amount > 0 {
            println!("⚠️  Warning: Could not allocate {} tokens due to liquidity constraints", remaining_amount);
        }

        Ok(splits)
    }

    /// Calculate weighted average impact across splits
    fn calculate_weighted_impact(&self, splits: &[TradeSplit]) -> f64 {
        if splits.is_empty() {
            return 0.0;
        }

        let total_weight: f64 = splits.iter().map(|s| s.allocation_percentage).sum();
        if total_weight == 0.0 {
            return 0.0;
        }

        let weighted_impact: f64 = splits.iter()
            .map(|s| s.price_impact * (s.allocation_percentage / total_weight))
            .sum();

        weighted_impact
    }

    /// Calculate market price from pools
    fn calculate_market_price(&self, pools: &[&PoolState]) -> f64 {
        if pools.is_empty() {
            return 0.0;
        }

        // Volume-weighted average price
        let total_volume: f64 = pools.iter()
            .map(|p| p.volume_24h.unwrap_or(0.0))
            .sum();

        if total_volume > 0.0 {
            pools.iter()
                .map(|p| p.current_price * (p.volume_24h.unwrap_or(0.0) / total_volume))
                .sum()
        } else {
            // Simple average if no volume data
            pools.iter().map(|p| p.current_price).sum::<f64>() / pools.len() as f64
        }
    }

    /// Calculate confidence score for the analysis
    fn calculate_confidence_score(&self, pool_impacts: &[PoolImpact]) -> f64 {
        if pool_impacts.is_empty() {
            return 0.0;
        }

        // Factors affecting confidence:
        // 1. Number of pools (more pools = higher confidence)
        // 2. Liquidity distribution (more balanced = higher confidence)
        // 3. Impact variance (lower variance = higher confidence)

        let pool_count_factor = (pool_impacts.len() as f64 / 5.0).min(1.0); // Max benefit at 5 pools
        
        let avg_impact: f64 = pool_impacts.iter().map(|p| p.impact_percentage).sum::<f64>() / pool_impacts.len() as f64;
        let impact_variance: f64 = pool_impacts.iter()
            .map(|p| (p.impact_percentage - avg_impact).powi(2))
            .sum::<f64>() / pool_impacts.len() as f64;
        
        let variance_factor = (1.0 / (1.0 + impact_variance / 10.0)).min(1.0);
        
        let liquidity_factor = pool_impacts.iter()
            .map(|p| (1.0 - p.liquidity_utilization).min(1.0))
            .sum::<f64>() / pool_impacts.len() as f64;

        // Weighted average of factors
        (pool_count_factor * 0.3 + variance_factor * 0.4 + liquidity_factor * 0.3).min(1.0)
    }

    /// Get trade size recommendation to stay under impact threshold
    pub async fn recommend_trade_size(
        &self,
        pools: &[PoolState],
        trade_direction: &TradeDirection,
        max_impact_threshold: f64,
        token_a: &Pubkey,
        token_b: &Pubkey,
    ) -> Result<u64> {
        // Binary search for optimal trade size
        let mut low = 0u64;
        let mut high = pools.iter()
            .map(|p| match trade_direction {
                TradeDirection::AtoB => p.token_a_balance,
                TradeDirection::BtoA => p.token_b_balance,
            })
            .sum::<u64>() / 10; // Start with 10% of total liquidity

        let mut best_size = 0u64;

        while low <= high {
            let mid = (low + high) / 2;
            
            if let Ok(analysis) = self.calculate_impact(pools, trade_direction, mid, token_a, token_b).await {
                if analysis.estimated_impact <= max_impact_threshold {
                    best_size = mid;
                    low = mid + 1;
                } else {
                    high = mid - 1;
                }
            } else {
                high = mid - 1;
            }
        }

        Ok(best_size)
    }

    /// Get impact statistics for monitoring
    pub fn get_impact_stats(&self, analyses: &[PriceImpactAnalysis]) -> HashMap<String, f64> {
        let mut stats = HashMap::new();
        
        if analyses.is_empty() {
            return stats;
        }

        let avg_impact: f64 = analyses.iter().map(|a| a.estimated_impact).sum::<f64>() / analyses.len() as f64;
        let max_impact: f64 = analyses.iter().map(|a| a.estimated_impact).fold(0.0, f64::max);
        let min_impact: f64 = analyses.iter().map(|a| a.estimated_impact).fold(f64::INFINITY, f64::min);
        let avg_confidence: f64 = analyses.iter().map(|a| a.confidence_score).sum::<f64>() / analyses.len() as f64;

        stats.insert("avg_impact".to_string(), avg_impact);
        stats.insert("max_impact".to_string(), max_impact);
        stats.insert("min_impact".to_string(), min_impact);
        stats.insert("avg_confidence".to_string(), avg_confidence);
        stats.insert("total_analyses".to_string(), analyses.len() as f64);

        stats
    }
}
