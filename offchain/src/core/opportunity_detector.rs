//! Enhanced Arbitrage Opportunity Detection System
//!
//! This module detects arbitrage opportunities across different DEXes and strategies,
//! integrating with live market data for real-time profit calculation and risk assessment.

use std::collections::HashMap;
use std::time::{Duration, Instant};
use std::sync::Arc;
use tokio::sync::{RwLock, mpsc};
use anyhow::{Result, anyhow};
use solana_sdk::pubkey::Pubkey;
use uuid::Uuid;
use serde::{Serialize, Deserialize};

use crate::core::priority_queue::{ArbitrageOpportunity, ArbitrageStrategy, OpportunityQueue};
use crate::core::token::{TokenModel, TokenPrice};
use crate::dex::dex_registry::DEXRegistry;
use crate::core::market_data_manager::{MarketDataManager, ComprehensiveAnalysis};
use crate::core::price_feed::{PriceFeedManager, PriceData};
use crate::core::market_depth::{MarketDepthAnalyzer, TradeDirection};
use crate::core::price_impact::PriceImpactCalculator;
use crate::core::volatility_detector::VolatilityDetector;

/// Enhanced configuration for opportunity detection with live market data
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct OpportunityDetectorConfig {
    pub min_profit_threshold_pct: f64,
    pub max_route_complexity: u8,
    pub opportunity_ttl_seconds: u64,
    pub min_liquidity_sol: f64,
    pub max_slippage_pct: f64,
    pub gas_estimation_enabled: bool,
    pub enable_multi_dex: bool,
    pub enable_two_hop: bool,
    pub enable_triangle: bool,
    // New live market data parameters
    pub use_live_pricing: bool,
    pub price_staleness_threshold_ms: u64,
    pub volatility_filter_enabled: bool,
    pub max_volatility_threshold: f64,
    pub market_depth_analysis_enabled: bool,
    pub price_impact_threshold_pct: f64,
    pub min_confidence_score: f64,
    pub enable_cross_dex_analysis: bool,
}

impl Default for OpportunityDetectorConfig {
    fn default() -> Self {
        Self {
            min_profit_threshold_pct: 0.5,
            max_route_complexity: 3,
            opportunity_ttl_seconds: 30,
            min_liquidity_sol: 0.1,
            max_slippage_pct: 1.0,
            gas_estimation_enabled: true,
            enable_multi_dex: true,
            enable_two_hop: true,
            enable_triangle: false, // Disabled by default due to complexity
            // Live market data defaults
            use_live_pricing: true,
            price_staleness_threshold_ms: 5000, // 5 seconds
            volatility_filter_enabled: true,
            max_volatility_threshold: 10.0, // 10% volatility threshold
            market_depth_analysis_enabled: true,
            price_impact_threshold_pct: 2.0, // 2% max price impact
            min_confidence_score: 0.7, // 70% minimum confidence
            enable_cross_dex_analysis: true,
        }
    }
}

/// Enhanced opportunity detection engine with live market data integration
pub struct OpportunityDetector {
    config: OpportunityDetectorConfig,
    token_model: Arc<RwLock<TokenModel>>,
    dex_registry: Arc<DEXRegistry>,
    opportunity_queue: Arc<OpportunityQueue>,
    gas_estimator: Arc<GasEstimator>,
    // Live market data components
    market_data_manager: Arc<MarketDataManager>,
    price_feed_manager: Arc<PriceFeedManager>,
    market_depth_analyzer: Arc<MarketDepthAnalyzer>,
    price_impact_calculator: Arc<PriceImpactCalculator>,
    volatility_detector: Arc<VolatilityDetector>,
    // Performance tracking
    opportunity_stats: Arc<RwLock<OpportunityStats>>,
}

/// Statistics tracking for opportunity detection performance
#[derive(Debug, Clone, Default)]
pub struct OpportunityStats {
    pub total_opportunities_detected: u64,
    pub multi_dex_opportunities: u64,
    pub two_hop_opportunities: u64,
    pub triangle_opportunities: u64,
    pub opportunities_filtered_by_volatility: u64,
    pub opportunities_filtered_by_price_impact: u64,
    pub opportunities_filtered_by_confidence: u64,
    pub average_profit_pct: f64,
    pub average_detection_time_ms: f64,
    pub last_scan_timestamp: Option<Instant>,
}

impl OpportunityDetector {
    /// Create a new enhanced opportunity detector with live market data integration
    pub fn new(
        config: OpportunityDetectorConfig,
        token_model: Arc<RwLock<TokenModel>>,
        dex_registry: Arc<DEXRegistry>,
        opportunity_queue: Arc<OpportunityQueue>,
        market_data_manager: Arc<MarketDataManager>,
        price_feed_manager: Arc<PriceFeedManager>,
        market_depth_analyzer: Arc<MarketDepthAnalyzer>,
        price_impact_calculator: Arc<PriceImpactCalculator>,
        volatility_detector: Arc<VolatilityDetector>,
    ) -> Self {
        Self {
            config,
            token_model,
            dex_registry,
            opportunity_queue,
            gas_estimator: Arc::new(GasEstimator::new()),
            market_data_manager,
            price_feed_manager,
            market_depth_analyzer,
            price_impact_calculator,
            volatility_detector,
            opportunity_stats: Arc::new(RwLock::new(OpportunityStats::default())),
        }
    }

    /// Enhanced scan for arbitrage opportunities using live market data
    pub async fn scan_for_opportunities(&self) -> Result<usize> {
        let scan_start = Instant::now();
        let mut total_opportunities = 0;
        let mut stats = self.opportunity_stats.write().await;

        // Update scan timestamp
        stats.last_scan_timestamp = Some(scan_start);
        drop(stats);

        // Get comprehensive market analysis if live data is enabled
        let market_analysis: Option<ComprehensiveAnalysis> = if self.config.use_live_pricing {
            // For now, we'll use a placeholder analysis since we need specific token pairs
            // In practice, this would be called with specific token pairs during opportunity detection
            None
        } else {
            None
        };

        // Get all tokens with price data (fallback to token model if live data unavailable)
        let all_tokens = {
            let token_model = self.token_model.read().await;
            let tokens = token_model.get_all_tokens_with_prices();
            drop(token_model);
            tokens
        };

        for (mint_str, token_prices) in all_tokens {
            let mint = Pubkey::try_from(mint_str.as_str())
                .map_err(|_| anyhow!("Invalid mint pubkey: {}", mint_str))?;

            // Pre-filter based on volatility if enabled
            if self.config.volatility_filter_enabled {
                if market_analysis.is_some() {
                    if self.should_filter_by_volatility(&mint, &market_analysis).await? {
                        let mut stats = self.opportunity_stats.write().await;
                        stats.opportunities_filtered_by_volatility += 1;
                        drop(stats);
                        continue;
                    }
                }
            }

            // Multi-DEX arbitrage opportunities
            if self.config.enable_multi_dex {
                let multi_dex_opps = self.detect_multi_dex_opportunities_enhanced(&mint, &token_prices, &market_analysis).await?;
                for opp in multi_dex_opps {
                    if self.validate_opportunity(&opp, &market_analysis).await? {
                        self.opportunity_queue.add_opportunity(opp).await?;
                        total_opportunities += 1;
                        let mut stats = self.opportunity_stats.write().await;
                        stats.multi_dex_opportunities += 1;
                        drop(stats);
                    }
                }
            }

            // Two-hop arbitrage opportunities
            if self.config.enable_two_hop {
                let two_hop_opps = self.detect_two_hop_opportunities_enhanced(&mint, &token_prices, &market_analysis).await?;
                for opp in two_hop_opps {
                    if self.validate_opportunity(&opp, &market_analysis).await? {
                        self.opportunity_queue.add_opportunity(opp).await?;
                        total_opportunities += 1;
                        let mut stats = self.opportunity_stats.write().await;
                        stats.two_hop_opportunities += 1;
                        drop(stats);
                    }
                }
            }

            // Triangle arbitrage opportunities
            if self.config.enable_triangle {
                let triangle_opps = self.detect_triangle_opportunities_enhanced(&mint, &token_prices, &market_analysis).await?;
                for opp in triangle_opps {
                    if self.validate_opportunity(&opp, &market_analysis).await? {
                        self.opportunity_queue.add_opportunity(opp).await?;
                        total_opportunities += 1;
                        let mut stats = self.opportunity_stats.write().await;
                        stats.triangle_opportunities += 1;
                        drop(stats);
                    }
                }
            }
        }

        // Update performance statistics
        let scan_duration = scan_start.elapsed();
        let mut stats = self.opportunity_stats.write().await;
        stats.total_opportunities_detected += total_opportunities as u64;
        stats.average_detection_time_ms = scan_duration.as_millis() as f64;
        drop(stats);

        Ok(total_opportunities)
    }



    /// Check if token should be filtered due to high volatility
    async fn should_filter_by_volatility(
        &self,
        mint: &Pubkey,
        _analysis: &Option<ComprehensiveAnalysis>,
    ) -> Result<bool> {
        // For now, use the volatility detector directly
        if let Some(volatility) = self.volatility_detector.get_volatility_analysis(&mint.to_string()).await {
            if volatility.current_volatility > self.config.max_volatility_threshold {
                return Ok(true);
            }
        }
        Ok(false)
    }

    /// Validate opportunity using live market data
    async fn validate_opportunity(
        &self,
        opportunity: &ArbitrageOpportunity,
        market_analysis: &Option<ComprehensiveAnalysis>,
    ) -> Result<bool> {
        // For now, perform basic validation
        // In a full implementation, this would use live market data from analysis

        // Check minimum liquidity requirement
        if opportunity.liquidity_available < self.config.min_liquidity_sol as u64 * 1_000_000_000 {
            let mut stats = self.opportunity_stats.write().await;
            stats.opportunities_filtered_by_volatility += 1;
            drop(stats);
            return Ok(false);
        }

        // Check minimum profit threshold
        if opportunity.estimated_profit_pct < self.config.min_profit_threshold_pct {
            let mut stats = self.opportunity_stats.write().await;
            stats.opportunities_filtered_by_confidence += 1;
            drop(stats);
            return Ok(false);
        }

        Ok(true)
    }

    /// Enhanced multi-DEX arbitrage detection with live market data
    async fn detect_multi_dex_opportunities_enhanced(
        &self,
        mint: &Pubkey,
        token_prices: &HashMap<String, TokenPrice>,
        market_analysis: &Option<ComprehensiveAnalysis>,
    ) -> Result<Vec<ArbitrageOpportunity>> {
        let mut opportunities = Vec::new();
        let dexes: Vec<&String> = token_prices.keys().collect();

        for i in 0..dexes.len() {
            for j in i + 1..dexes.len() {
                let dex1 = dexes[i];
                let dex2 = dexes[j];

                if let (Some(price1), Some(price2)) = (token_prices.get(dex1), token_prices.get(dex2)) {
                    // Use live pricing if available, otherwise fall back to token model prices
                    let (live_price1, live_price2, confidence_score) = if let Some(ref analysis) = market_analysis {
                        self.get_live_prices_for_dexes(mint, dex1, dex2, analysis).await?
                    } else {
                        (price1.price, price2.price, 1.0)
                    };

                    let price_diff_pct = ((live_price1 - live_price2).abs() / live_price2.min(live_price1)) * 100.0;

                    if price_diff_pct >= self.config.min_profit_threshold_pct {
                        // Determine buy and sell DEXes
                        let (buy_dex, sell_dex, buy_price, sell_price) = if live_price1 < live_price2 {
                            (dex1.clone(), dex2.clone(), live_price1, live_price2)
                        } else {
                            (dex2.clone(), dex1.clone(), live_price2, live_price1)
                        };

                        // Enhanced trade amount calculation with market depth analysis
                        let trade_amount = if self.config.market_depth_analysis_enabled && market_analysis.is_some() {
                            self.calculate_optimal_trade_amount_with_depth(mint, &buy_dex, &sell_dex, market_analysis.as_ref().unwrap()).await?
                        } else {
                            self.calculate_optimal_trade_amount(
                                price1.liquidity.min(price2.liquidity),
                                buy_price,
                            )
                        };

                        if trade_amount == 0 {
                            continue; // Skip if no viable trade amount
                        }

                        // Enhanced gas cost estimation
                        let estimated_gas_cost = if self.config.gas_estimation_enabled {
                            self.gas_estimator.estimate_multi_dex_gas(&buy_dex, &sell_dex).await?
                        } else {
                            200_000 // Default estimate
                        };

                        // Enhanced profit calculation with price impact consideration
                        let price_impact = if self.config.market_depth_analysis_enabled {
                            // For now, use a simple price impact estimation
                            0.1 // 0.1% default price impact
                        } else {
                            0.0
                        };

                        let adjusted_sell_price = sell_price * (1.0 - price_impact / 100.0);
                        let estimated_profit_sol = ((adjusted_sell_price - buy_price) * trade_amount as f64) / 1_000_000_000.0;
                        let estimated_profit_pct = price_diff_pct - price_impact;

                        // Only proceed if profit is still positive after price impact
                        if estimated_profit_pct > 0.0 && estimated_profit_sol > 0.0 {
                            let opportunity = ArbitrageOpportunity {
                                id: Uuid::new_v4().to_string(),
                                token_mint: *mint,
                                strategy_type: ArbitrageStrategy::MultiDex,
                                buy_dex: buy_dex.to_string(),
                                sell_dex: sell_dex.to_string(),
                                buy_price,
                                sell_price: adjusted_sell_price,
                                price_difference_pct: price_diff_pct,
                                estimated_profit_sol,
                                estimated_profit_pct,
                                liquidity_available: price1.liquidity.min(price2.liquidity),
                                detected_at: Instant::now(),
                                expires_at: Instant::now() + Duration::from_secs(self.config.opportunity_ttl_seconds),
                                trade_amount,
                                estimated_gas_cost,
                                route_complexity: 1,
                            };

                            opportunities.push(opportunity);
                        }
                    }
                }
            }
        }

        Ok(opportunities)
    }

    /// Get live prices for specific DEXes from market analysis
    async fn get_live_prices_for_dexes(
        &self,
        _mint: &Pubkey,
        _dex1: &str,
        _dex2: &str,
        _analysis: &ComprehensiveAnalysis,
    ) -> Result<(f64, f64, f64)> {
        // For now, return mock prices
        // In a full implementation, this would extract DEX-specific prices from analysis
        Ok((1.0, 1.01, 0.95)) // price1, price2, confidence
    }

    /// Calculate optimal trade amount using market depth analysis
    async fn calculate_optimal_trade_amount_with_depth(
        &self,
        _mint: &Pubkey,
        _buy_dex: &str,
        _sell_dex: &str,
        _analysis: &ComprehensiveAnalysis,
    ) -> Result<u64> {
        // For now, return a default trade amount
        // In a full implementation, this would use market depth analysis
        let default_liquidity = 1_000_000_000; // 1 SOL equivalent
        Ok(self.calculate_optimal_trade_amount(default_liquidity, 1.0))
    }

    /// Enhanced two-hop arbitrage detection with live market data
    async fn detect_two_hop_opportunities_enhanced(
        &self,
        mint: &Pubkey,
        _token_prices: &HashMap<String, TokenPrice>,
        market_analysis: &Option<ComprehensiveAnalysis>,
    ) -> Result<Vec<ArbitrageOpportunity>> {
        let mut opportunities = Vec::new();

        // Get common intermediate tokens (SOL, USDC, etc.)
        let intermediate_tokens = self.get_common_intermediate_tokens();

        for intermediate_mint in intermediate_tokens {
            // Enhanced two-hop route finding with live market data
            if let Some(opportunity) = self.find_two_hop_route_enhanced(mint, &intermediate_mint, market_analysis).await? {
                opportunities.push(opportunity);
            }
        }

        Ok(opportunities)
    }

    /// Enhanced triangle arbitrage detection with live market data
    async fn detect_triangle_opportunities_enhanced(
        &self,
        mint: &Pubkey,
        _token_prices: &HashMap<String, TokenPrice>,
        market_analysis: &Option<ComprehensiveAnalysis>,
    ) -> Result<Vec<ArbitrageOpportunity>> {
        let mut opportunities = Vec::new();

        // Get pairs of intermediate tokens for triangle routes
        let intermediate_tokens = self.get_common_intermediate_tokens();

        for i in 0..intermediate_tokens.len() {
            for j in i + 1..intermediate_tokens.len() {
                let token_b = intermediate_tokens[i];
                let token_c = intermediate_tokens[j];

                // Enhanced triangle route finding with live market data
                if let Some(opportunity) = self.find_triangle_route_enhanced(mint, &token_b, &token_c, market_analysis).await? {
                    opportunities.push(opportunity);
                }
            }
        }

        Ok(opportunities)
    }

    /// Calculate optimal trade amount based on liquidity and price
    fn calculate_optimal_trade_amount(&self, available_liquidity: u64, price: f64) -> u64 {
        let min_liquidity_lamports = (self.config.min_liquidity_sol * 1_000_000_000.0) as u64;
        
        if available_liquidity < min_liquidity_lamports {
            return 0;
        }

        // Use a conservative percentage of available liquidity to minimize slippage
        let conservative_amount = (available_liquidity as f64 * 0.1) as u64; // 10% of liquidity
        let max_trade_value_lamports = (1.0 * 1_000_000_000.0) as u64; // Max 1 SOL trade value
        let max_trade_amount = (max_trade_value_lamports as f64 / price) as u64;

        conservative_amount.min(max_trade_amount)
    }

    /// Get list of common intermediate tokens for multi-hop arbitrage
    fn get_common_intermediate_tokens(&self) -> Vec<Pubkey> {
        vec![
            // SOL
            Pubkey::try_from("So11111111111111111111111111111111111111112").unwrap(),
            // USDC
            Pubkey::try_from("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v").unwrap(),
            // USDT
            Pubkey::try_from("Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB").unwrap(),
        ]
    }

    /// Enhanced two-hop route finding with live market data
    async fn find_two_hop_route_enhanced(
        &self,
        start_mint: &Pubkey,
        intermediate_mint: &Pubkey,
        market_analysis: &Option<ComprehensiveAnalysis>,
    ) -> Result<Option<ArbitrageOpportunity>> {
        // Get available DEXes for both trading pairs
        let start_to_intermediate_dexes = self.get_available_dexes_for_pair(start_mint, intermediate_mint).await?;
        let intermediate_to_start_dexes = self.get_available_dexes_for_pair(intermediate_mint, start_mint).await?;

        if start_to_intermediate_dexes.is_empty() || intermediate_to_start_dexes.is_empty() {
            return Ok(None);
        }

        let mut best_opportunity: Option<ArbitrageOpportunity> = None;
        let mut best_profit = 0.0;

        // Try all combinations of DEXes for the two hops
        for first_dex in &start_to_intermediate_dexes {
            for second_dex in &intermediate_to_start_dexes {
                if let Some(opportunity) = self.calculate_two_hop_profit(
                    start_mint,
                    intermediate_mint,
                    first_dex,
                    second_dex,
                    market_analysis,
                ).await? {
                    if opportunity.estimated_profit_sol > best_profit {
                        best_profit = opportunity.estimated_profit_sol;
                        best_opportunity = Some(opportunity);
                    }
                }
            }
        }

        Ok(best_opportunity)
    }

    /// Enhanced triangle route finding with live market data
    async fn find_triangle_route_enhanced(
        &self,
        start_mint: &Pubkey,
        token_b: &Pubkey,
        token_c: &Pubkey,
        market_analysis: &Option<ComprehensiveAnalysis>,
    ) -> Result<Option<ArbitrageOpportunity>> {
        // Get available DEXes for all three trading pairs
        let dexes_a_to_b = self.get_available_dexes_for_pair(start_mint, token_b).await?;
        let dexes_b_to_c = self.get_available_dexes_for_pair(token_b, token_c).await?;
        let dexes_c_to_a = self.get_available_dexes_for_pair(token_c, start_mint).await?;

        if dexes_a_to_b.is_empty() || dexes_b_to_c.is_empty() || dexes_c_to_a.is_empty() {
            return Ok(None);
        }

        let mut best_opportunity: Option<ArbitrageOpportunity> = None;
        let mut best_profit = 0.0;

        // Try all combinations of DEXes for the three hops
        for dex1 in &dexes_a_to_b {
            for dex2 in &dexes_b_to_c {
                for dex3 in &dexes_c_to_a {
                    if let Some(opportunity) = self.calculate_triangle_profit(
                        start_mint,
                        token_b,
                        token_c,
                        dex1,
                        dex2,
                        dex3,
                        market_analysis,
                    ).await? {
                        if opportunity.estimated_profit_sol > best_profit {
                            best_profit = opportunity.estimated_profit_sol;
                            best_opportunity = Some(opportunity);
                        }
                    }
                }
            }
        }

        Ok(best_opportunity)
    }

    /// Get available DEXes for a trading pair
    async fn get_available_dexes_for_pair(
        &self,
        token_a: &Pubkey,
        token_b: &Pubkey,
    ) -> Result<Vec<String>> {
        // This would query the DEX registry to find which DEXes support this pair
        // For now, return common DEXes
        Ok(vec![
            "raydium".to_string(),
            "orca".to_string(),
            "meteora".to_string(),
            "whirlpool".to_string(),
        ])
    }

    /// Calculate profit for a two-hop arbitrage route
    async fn calculate_two_hop_profit(
        &self,
        start_mint: &Pubkey,
        intermediate_mint: &Pubkey,
        first_dex: &str,
        second_dex: &str,
        market_analysis: &Option<ComprehensiveAnalysis>,
    ) -> Result<Option<ArbitrageOpportunity>> {
        // Get prices for both hops
        let price_1_to_2 = self.get_pair_price(start_mint, intermediate_mint, first_dex, market_analysis).await?;
        let price_2_to_1 = self.get_pair_price(intermediate_mint, start_mint, second_dex, market_analysis).await?;

        if price_1_to_2.is_none() || price_2_to_1.is_none() {
            return Ok(None);
        }

        let (price1, liquidity1) = price_1_to_2.unwrap();
        let (price2, liquidity2) = price_2_to_1.unwrap();

        // Calculate if the route is profitable
        let route_multiplier = price1 * price2;
        if route_multiplier <= 1.0 {
            return Ok(None); // Not profitable
        }

        let profit_pct = (route_multiplier - 1.0) * 100.0;
        if profit_pct < self.config.min_profit_threshold_pct {
            return Ok(None);
        }

        // Calculate trade amount based on minimum liquidity
        let min_liquidity = liquidity1.min(liquidity2);
        let trade_amount = self.calculate_optimal_trade_amount(min_liquidity, 1.0);

        if trade_amount == 0 {
            return Ok(None);
        }

        // Estimate gas costs for two-hop
        let gas_cost = self.gas_estimator.estimate_two_hop_gas(first_dex, second_dex).await?;

        let estimated_profit_sol = ((route_multiplier - 1.0) * trade_amount as f64) / 1_000_000_000.0;

        let opportunity = ArbitrageOpportunity {
            id: Uuid::new_v4().to_string(),
            token_mint: *start_mint,
            strategy_type: ArbitrageStrategy::TwoHop {
                intermediate_token: *intermediate_mint
            },
            buy_dex: first_dex.to_string(),
            sell_dex: second_dex.to_string(),
            buy_price: price1,
            sell_price: price2,
            price_difference_pct: profit_pct,
            estimated_profit_sol,
            estimated_profit_pct: profit_pct,
            liquidity_available: min_liquidity,
            detected_at: Instant::now(),
            expires_at: Instant::now() + Duration::from_secs(self.config.opportunity_ttl_seconds),
            trade_amount,
            estimated_gas_cost: gas_cost,
            route_complexity: 2,
        };

        Ok(Some(opportunity))
    }

    /// Calculate profit for a triangle arbitrage route
    async fn calculate_triangle_profit(
        &self,
        start_mint: &Pubkey,
        token_b: &Pubkey,
        token_c: &Pubkey,
        dex1: &str,
        dex2: &str,
        dex3: &str,
        market_analysis: &Option<ComprehensiveAnalysis>,
    ) -> Result<Option<ArbitrageOpportunity>> {
        // Get prices for all three hops
        let price_a_to_b = self.get_pair_price(start_mint, token_b, dex1, market_analysis).await?;
        let price_b_to_c = self.get_pair_price(token_b, token_c, dex2, market_analysis).await?;
        let price_c_to_a = self.get_pair_price(token_c, start_mint, dex3, market_analysis).await?;

        if price_a_to_b.is_none() || price_b_to_c.is_none() || price_c_to_a.is_none() {
            return Ok(None);
        }

        let (price1, liquidity1) = price_a_to_b.unwrap();
        let (price2, liquidity2) = price_b_to_c.unwrap();
        let (price3, liquidity3) = price_c_to_a.unwrap();

        // Calculate if the triangle route is profitable
        let route_multiplier = price1 * price2 * price3;
        if route_multiplier <= 1.0 {
            return Ok(None); // Not profitable
        }

        let profit_pct = (route_multiplier - 1.0) * 100.0;
        if profit_pct < self.config.min_profit_threshold_pct {
            return Ok(None);
        }

        // Calculate trade amount based on minimum liquidity across all hops
        let min_liquidity = liquidity1.min(liquidity2).min(liquidity3);
        let trade_amount = self.calculate_optimal_trade_amount(min_liquidity, 1.0);

        if trade_amount == 0 {
            return Ok(None);
        }

        // Estimate gas costs for triangle arbitrage
        let gas_cost = self.gas_estimator.estimate_triangle_gas(dex1, dex2, dex3).await?;

        let estimated_profit_sol = ((route_multiplier - 1.0) * trade_amount as f64) / 1_000_000_000.0;

        let opportunity = ArbitrageOpportunity {
            id: Uuid::new_v4().to_string(),
            token_mint: *start_mint,
            strategy_type: ArbitrageStrategy::Triangle {
                token_b: *token_b,
                token_c: *token_c,
            },
            buy_dex: dex1.to_string(),
            sell_dex: format!("{}->{}->{}", dex1, dex2, dex3),
            buy_price: price1,
            sell_price: price3,
            price_difference_pct: profit_pct,
            estimated_profit_sol,
            estimated_profit_pct: profit_pct,
            liquidity_available: min_liquidity,
            detected_at: Instant::now(),
            expires_at: Instant::now() + Duration::from_secs(self.config.opportunity_ttl_seconds),
            trade_amount,
            estimated_gas_cost: gas_cost,
            route_complexity: 3,
        };

        Ok(Some(opportunity))
    }

    /// Get price for a specific trading pair on a specific DEX
    async fn get_pair_price(
        &self,
        token_a: &Pubkey,
        token_b: &Pubkey,
        dex: &str,
        market_analysis: &Option<ComprehensiveAnalysis>,
    ) -> Result<Option<(f64, u64)>> {
        // This is a simplified implementation
        // In practice, you would query the specific DEX for the pair price
        // For now, return a mock price if the tokens are in our common list

        let common_tokens = self.get_common_intermediate_tokens();
        if common_tokens.contains(token_a) || common_tokens.contains(token_b) {
            // Mock price and liquidity
            Ok(Some((1.0, 1_000_000_000))) // 1:1 price with 1 SOL liquidity
        } else {
            Ok(None)
        }
    }

    /// Get performance statistics
    pub async fn get_stats(&self) -> OpportunityStats {
        self.opportunity_stats.read().await.clone()
    }

    /// Reset performance statistics
    pub async fn reset_stats(&self) {
        let mut stats = self.opportunity_stats.write().await;
        *stats = OpportunityStats::default();
    }
}

/// Gas cost estimator for different arbitrage strategies
pub struct GasEstimator {
    base_gas_costs: HashMap<String, u64>,
}

impl GasEstimator {
    pub fn new() -> Self {
        let mut base_gas_costs = HashMap::new();
        
        // Base gas costs for different DEX operations (in compute units)
        base_gas_costs.insert("raydium".to_string(), 150_000);
        base_gas_costs.insert("orca".to_string(), 120_000);
        base_gas_costs.insert("meteora".to_string(), 180_000);
        base_gas_costs.insert("whirlpool".to_string(), 140_000);
        base_gas_costs.insert("pumpswap".to_string(), 100_000);

        Self { base_gas_costs }
    }

    /// Estimate gas cost for multi-DEX arbitrage
    pub async fn estimate_multi_dex_gas(&self, buy_dex: &str, sell_dex: &str) -> Result<u64> {
        let buy_cost = self.base_gas_costs.get(buy_dex).unwrap_or(&150_000);
        let sell_cost = self.base_gas_costs.get(sell_dex).unwrap_or(&150_000);
        
        // Add overhead for transaction coordination
        let total_cost = buy_cost + sell_cost + 50_000;
        
        Ok(total_cost)
    }

    /// Estimate gas cost for two-hop arbitrage
    pub async fn estimate_two_hop_gas(&self, dex1: &str, dex2: &str) -> Result<u64> {
        let cost1 = self.base_gas_costs.get(dex1).unwrap_or(&150_000);
        let cost2 = self.base_gas_costs.get(dex2).unwrap_or(&150_000);
        
        // Two-hop requires more coordination
        let total_cost = cost1 + cost2 + 100_000;
        
        Ok(total_cost)
    }

    /// Estimate gas cost for triangle arbitrage
    pub async fn estimate_triangle_gas(&self, dex1: &str, dex2: &str, dex3: &str) -> Result<u64> {
        let cost1 = self.base_gas_costs.get(dex1).unwrap_or(&150_000);
        let cost2 = self.base_gas_costs.get(dex2).unwrap_or(&150_000);
        let cost3 = self.base_gas_costs.get(dex3).unwrap_or(&150_000);
        
        // Triangle arbitrage has highest coordination overhead
        let total_cost = cost1 + cost2 + cost3 + 150_000;
        
        Ok(total_cost)
    }
}
