use std::sync::atomic::{<PERSON>Bool, AtomicU64, Ordering};
use std::sync::Arc;
use dashmap::DashMap;
use arc_swap::ArcSwap;
use tokio::time::Instant;
use serde::{Deserialize, Serialize};

/// Token tracking information with thread-safe access
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct TokenTrackingInfo {
    pub top_pnl: f64,
    pub last_price_check: u64, // Unix timestamp in milliseconds
    pub price_history: Vec<(f64, u64)>, // (price, timestamp) pairs
    pub buy_price: f64,
    pub buy_timestamp: u64,
    pub position_size: f64,
    pub status: TokenStatus,
}

/// Status of a token position
#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq)]
pub enum TokenStatus {
    Monitoring,
    Bought,
    Selling,
    Sold,
    Error,
}

impl Default for TokenTrackingInfo {
    fn default() -> Self {
        Self {
            top_pnl: 0.0,
            last_price_check: 0,
            price_history: Vec::new(),
            buy_price: 0.0,
            buy_timestamp: 0,
            position_size: 0.0,
            status: TokenStatus::Monitoring,
        }
    }
}

impl TokenTrackingInfo {
    /// Create new tracking info for a token
    pub fn new() -> Self {
        Self::default()
    }

    /// Update price and calculate PnL
    pub fn update_price(&mut self, new_price: f64) {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as u64;

        self.last_price_check = now;
        self.price_history.push((new_price, now));

        // Keep only last 100 price points to prevent memory growth
        if self.price_history.len() > 100 {
            self.price_history.remove(0);
        }

        // Calculate PnL if we have a buy price
        if self.buy_price > 0.0 {
            let pnl = ((new_price - self.buy_price) / self.buy_price) * 100.0;
            if pnl > self.top_pnl {
                self.top_pnl = pnl;
            }
        }
    }

    /// Mark as bought with price and size
    pub fn mark_bought(&mut self, price: f64, size: f64) {
        self.buy_price = price;
        self.position_size = size;
        self.status = TokenStatus::Bought;
        self.buy_timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as u64;
    }

    /// Get current PnL percentage
    pub fn current_pnl(&self) -> f64 {
        if let Some((latest_price, _)) = self.price_history.last() {
            if self.buy_price > 0.0 {
                return ((*latest_price - self.buy_price) / self.buy_price) * 100.0;
            }
        }
        0.0
    }

    /// Check if position has exceeded max hold time
    pub fn is_expired(&self, max_hold_time_ms: u64) -> bool {
        if self.buy_timestamp == 0 {
            return false;
        }

        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as u64;

        now.saturating_sub(self.buy_timestamp) > max_hold_time_ms
    }
}

/// Configuration for the arbitrage system
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct ArbitrageConfig {
    pub max_wait_time_ms: u64,
    pub min_profit_threshold_bps: u64,
    pub max_slippage_bps: u64,
    pub default_trade_amount: f64,
    pub max_total_position_size: f64,
    pub enable_emergency_liquidation: bool,
    pub simulation_mode: bool,
}

impl Default for ArbitrageConfig {
    fn default() -> Self {
        Self {
            max_wait_time_ms: 300_000, // 5 minutes
            min_profit_threshold_bps: 75,
            max_slippage_bps: 100,
            default_trade_amount: 0.05,
            max_total_position_size: 1.0,
            enable_emergency_liquidation: true,
            simulation_mode: false,
        }
    }
}

impl ArbitrageConfig {
    /// Load configuration from environment variables
    pub fn from_env() -> Self {
        Self {
            max_wait_time_ms: std::env::var("MAX_POSITION_HOLD_TIME_MS")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(300_000),
            min_profit_threshold_bps: std::env::var("MIN_PROFIT_THRESHOLD_BPS")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(75),
            max_slippage_bps: std::env::var("MAX_SLIPPAGE_BPS")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(100),
            default_trade_amount: std::env::var("DEFAULT_TRADE_AMOUNT")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(0.05),
            max_total_position_size: std::env::var("MAX_TOTAL_POSITION_SIZE_SOL")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(1.0),
            enable_emergency_liquidation: std::env::var("ENABLE_EMERGENCY_LIQUIDATION")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(true),
            simulation_mode: std::env::var("SIMULATION_MODE")
                .ok()
                .and_then(|s| s.parse().ok())
                .unwrap_or(false),
        }
    }
}

/// Lock-free global state management
#[derive(Debug)]
pub struct LockFreeGlobals {
    /// Token tracking using lock-free concurrent HashMap
    pub token_tracking: Arc<DashMap<String, TokenTrackingInfo>>,
    
    /// Atomic boolean for buying enabled/disabled
    pub buying_enabled: Arc<AtomicBool>,
    
    /// Emergency stop flag
    pub emergency_stop: Arc<AtomicBool>,
    
    /// Configuration that can be updated atomically
    pub config: Arc<ArcSwap<ArbitrageConfig>>,
    
    /// Total position size tracking (in SOL)
    pub total_position_size: Arc<AtomicU64>, // Stored as lamports for atomic operations
}

impl LockFreeGlobals {
    /// Create new lock-free globals with default configuration
    pub fn new() -> Self {
        Self {
            token_tracking: Arc::new(DashMap::new()),
            buying_enabled: Arc::new(AtomicBool::new(true)),
            emergency_stop: Arc::new(AtomicBool::new(false)),
            config: Arc::new(ArcSwap::new(Arc::new(ArbitrageConfig::default()))),
            total_position_size: Arc::new(AtomicU64::new(0)),
        }
    }

    /// Create with configuration from environment
    pub fn from_env() -> Self {
        let emergency_stop = std::env::var("EMERGENCY_STOP")
            .ok()
            .and_then(|s| s.parse().ok())
            .unwrap_or(false);

        Self {
            token_tracking: Arc::new(DashMap::new()),
            buying_enabled: Arc::new(AtomicBool::new(!emergency_stop)),
            emergency_stop: Arc::new(AtomicBool::new(emergency_stop)),
            config: Arc::new(ArcSwap::new(Arc::new(ArbitrageConfig::from_env()))),
            total_position_size: Arc::new(AtomicU64::new(0)),
        }
    }

    /// Check if buying is enabled
    pub fn is_buying_enabled(&self) -> bool {
        !self.emergency_stop.load(Ordering::Acquire) && 
        self.buying_enabled.load(Ordering::Acquire)
    }

    /// Enable or disable buying
    pub fn set_buying_enabled(&self, enabled: bool) {
        self.buying_enabled.store(enabled, Ordering::Release);
    }

    /// Set emergency stop
    pub fn set_emergency_stop(&self, stop: bool) {
        self.emergency_stop.store(stop, Ordering::Release);
        if stop {
            self.buying_enabled.store(false, Ordering::Release);
        }
    }

    /// Get current configuration
    pub fn get_config(&self) -> Arc<ArbitrageConfig> {
        self.config.load().clone()
    }

    /// Update configuration atomically
    pub fn update_config(&self, new_config: ArbitrageConfig) {
        self.config.store(Arc::new(new_config));
    }

    /// Add or update token tracking
    pub fn update_token_tracking(&self, mint: String, info: TokenTrackingInfo) {
        self.token_tracking.insert(mint, info);
    }

    /// Get token tracking info
    pub fn get_token_tracking(&self, mint: &str) -> Option<TokenTrackingInfo> {
        self.token_tracking.get(mint).map(|entry| entry.clone())
    }

    /// Remove token from tracking
    pub fn remove_token_tracking(&self, mint: &str) -> Option<TokenTrackingInfo> {
        self.token_tracking.remove(mint).map(|(_, info)| info)
    }

    /// Get all tracked tokens
    pub fn get_all_tracked_tokens(&self) -> Vec<(String, TokenTrackingInfo)> {
        self.token_tracking
            .iter()
            .map(|entry| (entry.key().clone(), entry.value().clone()))
            .collect()
    }

    /// Update token price
    pub fn update_token_price(&self, mint: &str, price: f64) {
        if let Some(mut entry) = self.token_tracking.get_mut(mint) {
            entry.update_price(price);
        }
    }

    /// Mark token as bought
    pub fn mark_token_bought(&self, mint: &str, price: f64, size: f64) {
        if let Some(mut entry) = self.token_tracking.get_mut(mint) {
            entry.mark_bought(price, size);
            
            // Update total position size
            let size_lamports = (size * 1_000_000_000.0) as u64; // Convert SOL to lamports
            self.total_position_size.fetch_add(size_lamports, Ordering::AcqRel);
        }
    }

    /// Mark token as sold
    pub fn mark_token_sold(&self, mint: &str) -> Option<f64> {
        if let Some(mut entry) = self.token_tracking.get_mut(mint) {
            let position_size = entry.position_size;
            entry.status = TokenStatus::Sold;
            entry.position_size = 0.0;
            
            // Update total position size
            let size_lamports = (position_size * 1_000_000_000.0) as u64;
            self.total_position_size.fetch_sub(size_lamports, Ordering::AcqRel);
            
            Some(position_size)
        } else {
            None
        }
    }

    /// Get current total position size in SOL
    pub fn get_total_position_size(&self) -> f64 {
        let lamports = self.total_position_size.load(Ordering::Acquire);
        lamports as f64 / 1_000_000_000.0
    }

    /// Check if we can add a new position
    pub fn can_add_position(&self, size: f64) -> bool {
        let config = self.get_config();
        let current_size = self.get_total_position_size();
        current_size + size <= config.max_total_position_size
    }

    /// Get expired tokens that need to be sold
    pub fn get_expired_tokens(&self) -> Vec<String> {
        let config = self.get_config();
        self.token_tracking
            .iter()
            .filter_map(|entry| {
                let (mint, info) = (entry.key(), entry.value());
                if info.status == TokenStatus::Bought && info.is_expired(config.max_wait_time_ms) {
                    Some(mint.clone())
                } else {
                    None
                }
            })
            .collect()
    }

    /// Clean up old tracking data
    pub fn cleanup_old_data(&self, max_age_ms: u64) {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as u64;

        self.token_tracking.retain(|_, info| {
            // Keep if recently updated or if we have an active position
            info.status == TokenStatus::Bought || 
            info.status == TokenStatus::Selling ||
            now.saturating_sub(info.last_price_check) < max_age_ms
        });
    }

    /// Get system statistics
    pub fn get_stats(&self) -> GlobalStats {
        let config = self.get_config();
        let tracked_tokens = self.token_tracking.len();
        let bought_tokens = self.token_tracking
            .iter()
            .filter(|entry| entry.value().status == TokenStatus::Bought)
            .count();

        GlobalStats {
            tracked_tokens,
            bought_tokens,
            total_position_size: self.get_total_position_size(),
            buying_enabled: self.is_buying_enabled(),
            emergency_stop: self.emergency_stop.load(Ordering::Acquire),
            max_position_size: config.max_total_position_size,
        }
    }
}

impl Default for LockFreeGlobals {
    fn default() -> Self {
        Self::new()
    }
}

/// Statistics about the global state
#[derive(Debug, Clone)]
pub struct GlobalStats {
    pub tracked_tokens: usize,
    pub bought_tokens: usize,
    pub total_position_size: f64,
    pub buying_enabled: bool,
    pub emergency_stop: bool,
    pub max_position_size: f64,
}

impl GlobalStats {
    /// Get a human-readable status string
    pub fn status_string(&self) -> String {
        format!(
            "Tracked: {}, Bought: {}, Position: {:.3}/{:.3} SOL, Buying: {}, Emergency: {}",
            self.tracked_tokens,
            self.bought_tokens,
            self.total_position_size,
            self.max_position_size,
            self.buying_enabled,
            self.emergency_stop
        )
    }

    /// Check if the system is under stress
    pub fn is_under_stress(&self) -> bool {
        self.total_position_size > self.max_position_size * 0.8 || // 80% of max position
        self.bought_tokens > 50 || // Too many active positions
        self.emergency_stop
    }
}
