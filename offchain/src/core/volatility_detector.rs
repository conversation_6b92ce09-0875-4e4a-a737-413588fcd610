//! Market Volatility Detection System
//! 
//! This module provides comprehensive volatility detection that monitors price movements,
//! identifies high volatility periods, and implements filtering mechanisms to avoid
//! trading during unstable market conditions.

use std::{
    collections::{HashMap, VecDeque},
    sync::Arc,
    time::{Duration, SystemTime, UNIX_EPOCH},
};
use anyhow::{Result, anyhow};
use tokio::{
    sync::RwLock,
    time::interval,
};
use serde::{Deserialize, Serialize};
use anchor_client::solana_sdk::pubkey::Pubkey;
use colored::Colorize;

use crate::core::{
    price_feed::{PriceFeedManager, AggregatedPrice},
    app_state::EnhancedAppState,
};

/// Volatility analysis result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VolatilityAnalysis {
    pub token_address: String,
    pub current_volatility: f64,
    pub volatility_percentile: f64,
    pub price_trend: PriceTrend,
    pub volatility_level: VolatilityLevel,
    pub trading_recommendation: TradingRecommendation,
    pub risk_score: f64,
    pub last_updated: u64,
    pub analysis_period_minutes: u32,
}

/// Price trend direction
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq, Eq)]
pub enum PriceTrend {
    StrongUptrend,
    Uptrend,
    Sideways,
    Downtrend,
    StrongDowntrend,
    Volatile, // High volatility with no clear direction
}

/// Volatility level classification
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum VolatilityLevel {
    VeryLow,
    Low,
    Normal,
    High,
    VeryHigh,
    Extreme,
}

/// Trading recommendation based on volatility
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum TradingRecommendation {
    SafeToTrade,
    CautiousTrading,
    ReducedSize,
    AvoidTrading,
    EmergencyStop,
}

/// Price data point for volatility calculation
#[derive(Debug, Clone)]
struct PricePoint {
    price: f64,
    timestamp: u64,
    volume: Option<f64>,
}

/// Volatility detection configuration
#[derive(Debug, Clone)]
pub struct VolatilityConfig {
    pub analysis_window_minutes: u32,
    pub price_history_size: usize,
    pub volatility_threshold_low: f64,
    pub volatility_threshold_high: f64,
    pub volatility_threshold_extreme: f64,
    pub trend_detection_periods: usize,
    pub update_interval_ms: u64,
    pub volume_weight_factor: f64,
    pub risk_score_threshold: f64,
}

impl Default for VolatilityConfig {
    fn default() -> Self {
        Self {
            analysis_window_minutes: 15,
            price_history_size: 100,
            volatility_threshold_low: 2.0,    // 2%
            volatility_threshold_high: 10.0,  // 10%
            volatility_threshold_extreme: 25.0, // 25%
            trend_detection_periods: 10,
            update_interval_ms: 5000, // 5 seconds
            volume_weight_factor: 0.3,
            risk_score_threshold: 0.7,
        }
    }
}

/// Volatility detector
pub struct VolatilityDetector {
    config: VolatilityConfig,
    price_history: Arc<RwLock<HashMap<String, VecDeque<PricePoint>>>>,
    volatility_cache: Arc<RwLock<HashMap<String, VolatilityAnalysis>>>,
    price_feed: Arc<PriceFeedManager>,
    app_state: Arc<EnhancedAppState>,
    monitored_tokens: Arc<RwLock<Vec<String>>>,
}

impl VolatilityDetector {
    /// Create a new volatility detector
    pub fn new(
        config: VolatilityConfig,
        price_feed: Arc<PriceFeedManager>,
        app_state: Arc<EnhancedAppState>,
    ) -> Self {
        Self {
            config,
            price_history: Arc::new(RwLock::new(HashMap::new())),
            volatility_cache: Arc::new(RwLock::new(HashMap::new())),
            price_feed,
            app_state,
            monitored_tokens: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Start the volatility detector
    pub async fn start(&self) -> Result<()> {
        println!("{}", "📈 Starting Market Volatility Detector".yellow().bold());
        println!("{}", "═".repeat(60).yellow());

        // Start periodic volatility analysis
        self.start_periodic_analysis().await?;

        println!("✅ Volatility Detector started successfully");
        Ok(())
    }

    /// Add a token to volatility monitoring
    pub async fn add_token(&self, token_address: String) -> Result<()> {
        let mut monitored = self.monitored_tokens.write().await;
        if !monitored.contains(&token_address) {
            monitored.push(token_address.clone());
            println!("📊 Added token {} to volatility monitoring", token_address);
        }
        Ok(())
    }

    /// Remove a token from monitoring
    pub async fn remove_token(&self, token_address: &str) -> Result<()> {
        let mut monitored = self.monitored_tokens.write().await;
        monitored.retain(|addr| addr != token_address);
        
        let mut history = self.price_history.write().await;
        history.remove(token_address);
        
        let mut cache = self.volatility_cache.write().await;
        cache.remove(token_address);
        
        println!("🗑️  Removed token {} from volatility monitoring", token_address);
        Ok(())
    }

    /// Get volatility analysis for a token
    pub async fn get_volatility_analysis(&self, token_address: &str) -> Option<VolatilityAnalysis> {
        let cache = self.volatility_cache.read().await;
        cache.get(token_address).cloned()
    }

    /// Check if trading is safe based on volatility
    pub async fn is_safe_to_trade(&self, token_address: &str) -> bool {
        if let Some(analysis) = self.get_volatility_analysis(token_address).await {
            matches!(analysis.trading_recommendation, 
                TradingRecommendation::SafeToTrade | TradingRecommendation::CautiousTrading)
        } else {
            false // Default to unsafe if no analysis available
        }
    }

    /// Get recommended trade size multiplier based on volatility
    pub async fn get_size_multiplier(&self, token_address: &str) -> f64 {
        if let Some(analysis) = self.get_volatility_analysis(token_address).await {
            match analysis.trading_recommendation {
                TradingRecommendation::SafeToTrade => 1.0,
                TradingRecommendation::CautiousTrading => 0.7,
                TradingRecommendation::ReducedSize => 0.3,
                TradingRecommendation::AvoidTrading => 0.0,
                TradingRecommendation::EmergencyStop => 0.0,
            }
        } else {
            0.5 // Conservative default
        }
    }

    /// Start periodic volatility analysis
    async fn start_periodic_analysis(&self) -> Result<()> {
        let price_history = self.price_history.clone();
        let volatility_cache = self.volatility_cache.clone();
        let price_feed = self.price_feed.clone();
        let monitored_tokens = self.monitored_tokens.clone();
        let config = self.config.clone();

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_millis(config.update_interval_ms));
            
            loop {
                interval.tick().await;
                
                let tokens = monitored_tokens.read().await.clone();
                
                for token_address in tokens {
                    // Update price history
                    if let Ok(Some(price_data)) = price_feed.get_price(&token_address).await {
                        Self::update_price_history(&price_history, &token_address, &price_data, &config).await;
                    }
                    
                    // Analyze volatility
                    if let Ok(analysis) = Self::analyze_token_volatility(
                        &price_history,
                        &token_address,
                        &config,
                    ).await {
                        let mut cache = volatility_cache.write().await;
                        cache.insert(token_address.clone(), analysis);
                    }
                }
            }
        });

        Ok(())
    }

    /// Update price history for a token
    async fn update_price_history(
        price_history: &Arc<RwLock<HashMap<String, VecDeque<PricePoint>>>>,
        token_address: &str,
        price_data: &AggregatedPrice,
        config: &VolatilityConfig,
    ) {
        let mut history = price_history.write().await;
        let token_history = history.entry(token_address.to_string()).or_insert_with(VecDeque::new);
        
        // Add new price point
        token_history.push_back(PricePoint {
            price: price_data.weighted_price,
            timestamp: price_data.last_updated,
            volume: price_data.sources.first().and_then(|s| s.volume_24h),
        });
        
        // Maintain history size
        while token_history.len() > config.price_history_size {
            token_history.pop_front();
        }
    }

    /// Analyze volatility for a specific token
    async fn analyze_token_volatility(
        price_history: &Arc<RwLock<HashMap<String, VecDeque<PricePoint>>>>,
        token_address: &str,
        config: &VolatilityConfig,
    ) -> Result<VolatilityAnalysis> {
        let history = price_history.read().await;
        let token_history = history.get(token_address)
            .ok_or_else(|| anyhow!("No price history for token {}", token_address))?;

        if token_history.len() < 2 {
            return Err(anyhow!("Insufficient price history for volatility analysis"));
        }

        // Calculate returns
        let returns = Self::calculate_returns(token_history);
        
        // Calculate volatility metrics
        let current_volatility = Self::calculate_volatility(&returns, config);
        let volatility_percentile = Self::calculate_volatility_percentile(&returns, current_volatility);
        
        // Determine trend
        let price_trend = Self::determine_price_trend(token_history, config);
        
        // Classify volatility level
        let volatility_level = Self::classify_volatility_level(current_volatility, config);
        
        // Generate trading recommendation
        let trading_recommendation = Self::generate_trading_recommendation(
            &volatility_level,
            &price_trend,
            current_volatility,
            config,
        );
        
        // Calculate risk score
        let risk_score = Self::calculate_risk_score(
            current_volatility,
            &volatility_level,
            &price_trend,
            config,
        );

        Ok(VolatilityAnalysis {
            token_address: token_address.to_string(),
            current_volatility,
            volatility_percentile,
            price_trend,
            volatility_level,
            trading_recommendation,
            risk_score,
            last_updated: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
            analysis_period_minutes: config.analysis_window_minutes,
        })
    }

    /// Calculate price returns
    fn calculate_returns(price_history: &VecDeque<PricePoint>) -> Vec<f64> {
        let mut returns = Vec::new();
        
        for i in 1..price_history.len() {
            let prev_price = price_history[i - 1].price;
            let curr_price = price_history[i].price;
            
            if prev_price > 0.0 {
                let return_pct = (curr_price - prev_price) / prev_price;
                returns.push(return_pct);
            }
        }
        
        returns
    }

    /// Calculate volatility (standard deviation of returns)
    fn calculate_volatility(returns: &[f64], config: &VolatilityConfig) -> f64 {
        if returns.is_empty() {
            return 0.0;
        }

        // Calculate mean return
        let mean_return = returns.iter().sum::<f64>() / returns.len() as f64;
        
        // Calculate variance
        let variance = returns.iter()
            .map(|r| (r - mean_return).powi(2))
            .sum::<f64>() / returns.len() as f64;
        
        // Standard deviation as percentage
        let volatility = variance.sqrt() * 100.0;
        
        // Annualize volatility (approximate)
        let periods_per_year = 365.0 * 24.0 * 60.0 / config.analysis_window_minutes as f64;
        volatility * periods_per_year.sqrt()
    }

    /// Calculate volatility percentile
    fn calculate_volatility_percentile(returns: &[f64], current_volatility: f64) -> f64 {
        if returns.len() < 10 {
            return 50.0; // Default to median if insufficient data
        }

        // Calculate rolling volatilities
        let window_size = 10;
        let mut rolling_volatilities = Vec::new();
        
        for i in window_size..=returns.len() {
            let window = &returns[i - window_size..i];
            let vol = Self::calculate_volatility_simple(window);
            rolling_volatilities.push(vol);
        }
        
        // Calculate percentile
        rolling_volatilities.sort_by(|a, b| a.partial_cmp(b).unwrap());
        let position = rolling_volatilities.iter()
            .position(|&v| v >= current_volatility)
            .unwrap_or(rolling_volatilities.len());
        
        (position as f64 / rolling_volatilities.len() as f64) * 100.0
    }

    /// Simple volatility calculation for rolling windows
    fn calculate_volatility_simple(returns: &[f64]) -> f64 {
        if returns.is_empty() {
            return 0.0;
        }

        let mean = returns.iter().sum::<f64>() / returns.len() as f64;
        let variance = returns.iter()
            .map(|r| (r - mean).powi(2))
            .sum::<f64>() / returns.len() as f64;
        
        variance.sqrt() * 100.0
    }

    /// Determine price trend
    fn determine_price_trend(price_history: &VecDeque<PricePoint>, config: &VolatilityConfig) -> PriceTrend {
        let len = price_history.len();
        if len < config.trend_detection_periods {
            return PriceTrend::Sideways;
        }

        let recent_prices: Vec<f64> = price_history.iter()
            .rev()
            .take(config.trend_detection_periods)
            .map(|p| p.price)
            .collect();

        // Calculate trend strength
        let first_price = recent_prices.last().unwrap();
        let last_price = recent_prices.first().unwrap();
        let price_change_pct = (last_price - first_price) / first_price * 100.0;

        // Calculate trend consistency (how many periods support the trend)
        let mut up_periods = 0;
        let mut down_periods = 0;
        
        for i in 1..recent_prices.len() {
            if recent_prices[i - 1] < recent_prices[i] {
                up_periods += 1;
            } else if recent_prices[i - 1] > recent_prices[i] {
                down_periods += 1;
            }
        }

        let trend_consistency = (up_periods.max(down_periods) as f64 / (recent_prices.len() - 1) as f64) * 100.0;

        // Classify trend
        match (price_change_pct, trend_consistency) {
            (change, consistency) if change > 5.0 && consistency > 70.0 => PriceTrend::StrongUptrend,
            (change, consistency) if change > 2.0 && consistency > 60.0 => PriceTrend::Uptrend,
            (change, consistency) if change < -5.0 && consistency > 70.0 => PriceTrend::StrongDowntrend,
            (change, consistency) if change < -2.0 && consistency > 60.0 => PriceTrend::Downtrend,
            (_, consistency) if consistency < 40.0 => PriceTrend::Volatile,
            _ => PriceTrend::Sideways,
        }
    }

    /// Classify volatility level
    fn classify_volatility_level(volatility: f64, config: &VolatilityConfig) -> VolatilityLevel {
        match volatility {
            v if v >= config.volatility_threshold_extreme => VolatilityLevel::Extreme,
            v if v >= config.volatility_threshold_high => VolatilityLevel::VeryHigh,
            v if v >= config.volatility_threshold_high * 0.7 => VolatilityLevel::High,
            v if v >= config.volatility_threshold_low => VolatilityLevel::Normal,
            v if v >= config.volatility_threshold_low * 0.5 => VolatilityLevel::Low,
            _ => VolatilityLevel::VeryLow,
        }
    }

    /// Generate trading recommendation
    fn generate_trading_recommendation(
        volatility_level: &VolatilityLevel,
        price_trend: &PriceTrend,
        current_volatility: f64,
        config: &VolatilityConfig,
    ) -> TradingRecommendation {
        match volatility_level {
            VolatilityLevel::Extreme => TradingRecommendation::EmergencyStop,
            VolatilityLevel::VeryHigh => {
                if matches!(price_trend, PriceTrend::Volatile) {
                    TradingRecommendation::AvoidTrading
                } else {
                    TradingRecommendation::ReducedSize
                }
            }
            VolatilityLevel::High => TradingRecommendation::ReducedSize,
            VolatilityLevel::Normal => {
                if matches!(price_trend, PriceTrend::Volatile) {
                    TradingRecommendation::CautiousTrading
                } else {
                    TradingRecommendation::SafeToTrade
                }
            }
            VolatilityLevel::Low | VolatilityLevel::VeryLow => TradingRecommendation::SafeToTrade,
        }
    }

    /// Calculate risk score
    fn calculate_risk_score(
        volatility: f64,
        volatility_level: &VolatilityLevel,
        price_trend: &PriceTrend,
        config: &VolatilityConfig,
    ) -> f64 {
        let volatility_score = (volatility / config.volatility_threshold_extreme).min(1.0);
        
        let trend_score = match price_trend {
            PriceTrend::Volatile => 0.8,
            PriceTrend::StrongUptrend | PriceTrend::StrongDowntrend => 0.6,
            PriceTrend::Uptrend | PriceTrend::Downtrend => 0.4,
            PriceTrend::Sideways => 0.2,
        };

        let level_score = match volatility_level {
            VolatilityLevel::Extreme => 1.0,
            VolatilityLevel::VeryHigh => 0.8,
            VolatilityLevel::High => 0.6,
            VolatilityLevel::Normal => 0.4,
            VolatilityLevel::Low => 0.2,
            VolatilityLevel::VeryLow => 0.1,
        };

        // Weighted combination
        (volatility_score * 0.4 + trend_score * 0.3 + level_score * 0.3).min(1.0)
    }

    /// Get volatility statistics
    pub async fn get_volatility_stats(&self) -> HashMap<String, f64> {
        let cache = self.volatility_cache.read().await;
        let mut stats = HashMap::new();
        
        if cache.is_empty() {
            return stats;
        }

        let analyses: Vec<&VolatilityAnalysis> = cache.values().collect();
        
        let avg_volatility = analyses.iter().map(|a| a.current_volatility).sum::<f64>() / analyses.len() as f64;
        let max_volatility = analyses.iter().map(|a| a.current_volatility).fold(0.0, f64::max);
        let avg_risk_score = analyses.iter().map(|a| a.risk_score).sum::<f64>() / analyses.len() as f64;
        
        let safe_tokens = analyses.iter()
            .filter(|a| matches!(a.trading_recommendation, TradingRecommendation::SafeToTrade))
            .count() as f64;
        
        stats.insert("avg_volatility".to_string(), avg_volatility);
        stats.insert("max_volatility".to_string(), max_volatility);
        stats.insert("avg_risk_score".to_string(), avg_risk_score);
        stats.insert("safe_tokens_pct".to_string(), (safe_tokens / analyses.len() as f64) * 100.0);
        stats.insert("monitored_tokens".to_string(), analyses.len() as f64);
        
        stats
    }
}
