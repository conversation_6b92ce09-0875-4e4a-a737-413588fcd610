//! Risk Management System for Solana Arbitrage Bot
//! 
//! This module provides comprehensive risk management including:
//! - Position limits and capital management
//! - Stop-loss and emergency controls
//! - Real-time risk monitoring
//! - Market condition safety checks
//! - Transaction safety validation

use std::sync::Arc;
use std::sync::atomic::{AtomicBool, AtomicU64, Ordering};
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH, Duration};
use tokio::sync::{RwLock, Mutex};
use serde::{Serialize, Deserialize};
use anyhow::{Result, anyhow};
use solana_sdk::pubkey::Pubkey;
use chrono::{DateTime, Utc, Timelike};

use crate::core::{
    live_config::LiveTradingConfig,
    lock_free_globals::LockFreeGlobals,
    market_data_manager::MarketDataManager,
    volatility_detector::VolatilityAnalysis,
    price_impact::PriceImpactAnalysis,
};

/// Risk management configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskConfig {
    /// Position limits
    pub max_position_size_sol: f64,
    pub max_total_exposure_sol: f64,
    pub max_single_trade_sol: f64,
    pub max_positions_per_token: u32,
    
    /// Daily limits
    pub daily_loss_limit_sol: f64,
    pub daily_trade_limit: u32,
    pub daily_volume_limit_sol: f64,
    
    /// Stop-loss parameters
    pub stop_loss_percentage: f64,
    pub trailing_stop_percentage: f64,
    pub max_drawdown_percentage: f64,
    
    /// Emergency controls
    pub emergency_stop_loss_percentage: f64,
    pub circuit_breaker_loss_threshold: f64,
    pub volatility_halt_threshold: f64,
    
    /// Market condition filters
    pub min_liquidity_threshold: u64,
    pub max_slippage_tolerance: f64,
    pub max_price_impact_tolerance: f64,
    pub min_confidence_score: f64,
    
    /// Time-based controls
    pub position_timeout_minutes: u64,
    pub cool_down_period_seconds: u64,
    pub trading_hours_start: u32, // Hour of day (0-23)
    pub trading_hours_end: u32,   // Hour of day (0-23)
    pub enforce_trading_hours: bool,

    /// Market condition safety
    pub max_volatility_percentage: f64,
    pub max_slippage_percentage: f64,

    /// Transaction safety
    pub min_profit_threshold_sol: f64,
    pub max_gas_to_profit_ratio: f64,
    pub min_trade_size_sol: f64,
    pub enforce_cool_down: bool,
}

impl Default for RiskConfig {
    fn default() -> Self {
        Self {
            max_position_size_sol: 1.0,
            max_total_exposure_sol: 5.0,
            max_single_trade_sol: 0.5,
            max_positions_per_token: 3,
            
            daily_loss_limit_sol: 2.0,
            daily_trade_limit: 100,
            daily_volume_limit_sol: 50.0,
            
            stop_loss_percentage: 5.0,
            trailing_stop_percentage: 3.0,
            max_drawdown_percentage: 10.0,
            
            emergency_stop_loss_percentage: 15.0,
            circuit_breaker_loss_threshold: 20.0,
            volatility_halt_threshold: 50.0,
            
            min_liquidity_threshold: 10_000,
            max_slippage_tolerance: 2.0,
            max_price_impact_tolerance: 1.0,
            min_confidence_score: 0.7,
            
            position_timeout_minutes: 30,
            cool_down_period_seconds: 60,
            trading_hours_start: 0,  // 24/7 trading by default
            trading_hours_end: 23,
            enforce_trading_hours: false,

            max_volatility_percentage: 20.0, // 20% max volatility
            max_slippage_percentage: 3.0,    // 3% max slippage

            min_profit_threshold_sol: 0.001, // Minimum 0.001 SOL profit
            max_gas_to_profit_ratio: 0.3,    // Max 30% gas to profit ratio
            min_trade_size_sol: 0.01,        // Minimum 0.01 SOL trade
            enforce_cool_down: true,
        }
    }
}

/// Position tracking information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Position {
    pub token_mint: Pubkey,
    pub amount_sol: f64,
    pub entry_price: f64,
    pub current_price: f64,
    pub unrealized_pnl: f64,
    pub entry_timestamp: u64,
    pub last_update_timestamp: u64,
    pub stop_loss_price: Option<f64>,
    pub trailing_stop_price: Option<f64>,
    pub is_emergency_exit: bool,
}

impl Position {
    pub fn new(token_mint: Pubkey, amount_sol: f64, entry_price: f64) -> Self {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
            
        Self {
            token_mint,
            amount_sol,
            entry_price,
            current_price: entry_price,
            unrealized_pnl: 0.0,
            entry_timestamp: timestamp,
            last_update_timestamp: timestamp,
            stop_loss_price: None,
            trailing_stop_price: None,
            is_emergency_exit: false,
        }
    }
    
    pub fn update_price(&mut self, new_price: f64) {
        self.current_price = new_price;
        self.unrealized_pnl = (new_price - self.entry_price) / self.entry_price * 100.0;
        self.last_update_timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
    }
    
    pub fn is_expired(&self, timeout_minutes: u64) -> bool {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        now.saturating_sub(self.entry_timestamp) > timeout_minutes * 60
    }
    
    pub fn should_stop_loss(&self, stop_loss_percentage: f64) -> bool {
        self.unrealized_pnl <= -stop_loss_percentage
    }
    
    pub fn should_trailing_stop(&self, trailing_stop_percentage: f64) -> bool {
        if let Some(trailing_price) = self.trailing_stop_price {
            self.current_price <= trailing_price
        } else {
            false
        }
    }
}

/// Daily trading statistics
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct DailyStats {
    pub date: String,
    pub total_trades: u32,
    pub total_volume_sol: f64,
    pub realized_pnl_sol: f64,
    pub unrealized_pnl_sol: f64,
    pub max_drawdown: f64,
    pub largest_loss: f64,
    pub largest_gain: f64,
    pub successful_trades: u32,
    pub failed_trades: u32,
    pub total_profit_sol: f64,
    pub total_gas_sol: f64,
    pub trade_count: u32,
    pub volume_sol: f64,
}

/// Risk assessment result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAssessment {
    pub is_safe_to_trade: bool,
    pub risk_score: f64, // 0.0 (safe) to 1.0 (very risky)
    pub warnings: Vec<String>,
    pub blocking_issues: Vec<String>,
    pub recommended_position_size: f64,
    pub max_allowed_slippage: f64,
}

/// Main risk management system
pub struct RiskManager {
    config: Arc<RwLock<RiskConfig>>,
    positions: Arc<RwLock<HashMap<Pubkey, Position>>>,
    daily_stats: Arc<RwLock<DailyStats>>,
    emergency_stop: Arc<AtomicBool>,
    circuit_breaker_active: Arc<AtomicBool>,
    total_exposure: Arc<AtomicU64>, // in lamports
    daily_loss_tracker: Arc<AtomicU64>, // in lamports
    last_trade_timestamp: Arc<AtomicU64>,
    globals: Arc<LockFreeGlobals>,
    market_data: Arc<MarketDataManager>,
}

impl Clone for RiskManager {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            positions: self.positions.clone(),
            daily_stats: self.daily_stats.clone(),
            emergency_stop: self.emergency_stop.clone(),
            circuit_breaker_active: self.circuit_breaker_active.clone(),
            total_exposure: self.total_exposure.clone(),
            daily_loss_tracker: self.daily_loss_tracker.clone(),
            last_trade_timestamp: self.last_trade_timestamp.clone(),
            globals: self.globals.clone(),
            market_data: self.market_data.clone(),
        }
    }
}

impl RiskManager {
    pub fn new(
        config: RiskConfig,
        globals: Arc<LockFreeGlobals>,
        market_data: Arc<MarketDataManager>,
    ) -> Self {
        Self {
            config: Arc::new(RwLock::new(config)),
            positions: Arc::new(RwLock::new(HashMap::new())),
            daily_stats: Arc::new(RwLock::new(DailyStats::default())),
            emergency_stop: Arc::new(AtomicBool::new(false)),
            circuit_breaker_active: Arc::new(AtomicBool::new(false)),
            total_exposure: Arc::new(AtomicU64::new(0)),
            daily_loss_tracker: Arc::new(AtomicU64::new(0)),
            last_trade_timestamp: Arc::new(AtomicU64::new(0)),
            globals,
            market_data,
        }
    }
    
    /// Check if trading is allowed based on risk parameters
    pub async fn is_trading_allowed(&self) -> bool {
        // Check emergency stop
        if self.emergency_stop.load(Ordering::Acquire) {
            return false;
        }
        
        // Check circuit breaker
        if self.circuit_breaker_active.load(Ordering::Acquire) {
            return false;
        }
        
        // Check global emergency stop
        if !self.globals.is_buying_enabled() {
            return false;
        }
        
        // Check trading hours
        if !self.is_within_trading_hours().await {
            return false;
        }
        
        // Check daily limits
        if !self.check_daily_limits().await {
            return false;
        }
        
        true
    }
    
    /// Assess risk for a potential trade
    pub async fn assess_trade_risk(
        &self,
        token_mint: Pubkey,
        trade_amount_sol: f64,
        expected_slippage: f64,
        price_impact: &PriceImpactAnalysis,
        volatility: Option<&VolatilityAnalysis>,
    ) -> Result<RiskAssessment> {
        let config = self.config.read().await;
        let mut warnings = Vec::new();
        let mut blocking_issues = Vec::new();
        let mut risk_score = 0.0;
        
        // Check position size limits
        if trade_amount_sol > config.max_single_trade_sol {
            blocking_issues.push(format!(
                "Trade amount ({:.4} SOL) exceeds max single trade limit ({:.4} SOL)",
                trade_amount_sol, config.max_single_trade_sol
            ));
        }
        
        // Check total exposure
        let current_exposure = self.total_exposure.load(Ordering::Acquire) as f64 / 1e9;
        if current_exposure + trade_amount_sol > config.max_total_exposure_sol {
            blocking_issues.push(format!(
                "Trade would exceed total exposure limit ({:.4} + {:.4} > {:.4} SOL)",
                current_exposure, trade_amount_sol, config.max_total_exposure_sol
            ));
        }
        
        // Check slippage tolerance
        if expected_slippage > config.max_slippage_tolerance {
            blocking_issues.push(format!(
                "Expected slippage ({:.2}%) exceeds tolerance ({:.2}%)",
                expected_slippage, config.max_slippage_tolerance
            ));
        }
        
        // Check price impact
        if price_impact.estimated_impact > config.max_price_impact_tolerance {
            warnings.push(format!(
                "High price impact: {:.2}%",
                price_impact.estimated_impact
            ));
            risk_score += 0.3;
        }
        
        // Check volatility
        if let Some(vol) = volatility {
            if vol.risk_score > 0.7 {
                warnings.push(format!(
                    "High volatility detected: risk score {:.2}",
                    vol.risk_score
                ));
                risk_score += vol.risk_score * 0.4;
            }
        }
        
        // Check price impact (indicating liquidity)
        if price_impact.estimated_impact > config.max_slippage_percentage / 100.0 {
            warnings.push(format!(
                "High price impact: {:.2}% > {:.2}%",
                price_impact.estimated_impact * 100.0, config.max_slippage_percentage
            ));
            risk_score += 0.2;
        }
        
        // Calculate recommended position size
        let recommended_size = self.calculate_recommended_position_size(
            trade_amount_sol,
            risk_score,
            &config,
        ).await;
        
        // Calculate max allowed slippage
        let max_slippage = (config.max_slippage_tolerance * (1.0 - risk_score * 0.5))
            .max(0.1); // Minimum 0.1%
        
        Ok(RiskAssessment {
            is_safe_to_trade: blocking_issues.is_empty() && risk_score < 0.8,
            risk_score: risk_score.min(1.0),
            warnings,
            blocking_issues,
            recommended_position_size: recommended_size,
            max_allowed_slippage: max_slippage,
        })
    }

    /// Open a new position
    pub async fn open_position(
        &self,
        token_mint: Pubkey,
        amount_sol: f64,
        entry_price: f64,
    ) -> Result<()> {
        let config = self.config.read().await;
        let mut positions = self.positions.write().await;

        // Check if we already have too many positions for this token
        let existing_positions = positions.values()
            .filter(|p| p.token_mint == token_mint)
            .count();

        if existing_positions >= config.max_positions_per_token as usize {
            return Err(anyhow!(
                "Too many positions for token {}: {} >= {}",
                token_mint, existing_positions, config.max_positions_per_token
            ));
        }

        // Create new position
        let position = Position::new(token_mint, amount_sol, entry_price);

        // Set stop-loss if configured
        let mut position = position;
        if config.stop_loss_percentage > 0.0 {
            position.stop_loss_price = Some(
                entry_price * (1.0 - config.stop_loss_percentage / 100.0)
            );
        }

        positions.insert(token_mint, position);

        // Update exposure tracking
        let amount_lamports = (amount_sol * 1e9) as u64;
        self.total_exposure.fetch_add(amount_lamports, Ordering::AcqRel);

        // Update daily stats
        let mut daily_stats = self.daily_stats.write().await;
        daily_stats.total_trades += 1;
        daily_stats.total_volume_sol += amount_sol;

        Ok(())
    }

    /// Close a position
    pub async fn close_position(
        &self,
        token_mint: Pubkey,
        exit_price: f64,
        reason: &str,
    ) -> Result<f64> {
        let mut positions = self.positions.write().await;

        if let Some(position) = positions.remove(&token_mint) {
            // Calculate realized P&L
            let realized_pnl = (exit_price - position.entry_price) / position.entry_price * 100.0;
            let pnl_sol = position.amount_sol * realized_pnl / 100.0;

            // Update exposure tracking
            let amount_lamports = (position.amount_sol * 1e9) as u64;
            self.total_exposure.fetch_sub(amount_lamports, Ordering::AcqRel);

            // Update daily stats
            let mut daily_stats = self.daily_stats.write().await;
            daily_stats.realized_pnl_sol += pnl_sol;

            if pnl_sol < 0.0 {
                daily_stats.largest_loss = daily_stats.largest_loss.min(pnl_sol);

                // Update daily loss tracker
                let loss_lamports = (-pnl_sol * 1e9) as u64;
                self.daily_loss_tracker.fetch_add(loss_lamports, Ordering::AcqRel);
            } else {
                daily_stats.largest_gain = daily_stats.largest_gain.max(pnl_sol);
            }

            println!("📊 Position closed: {} | P&L: {:.4} SOL ({:.2}%) | Reason: {}",
                token_mint, pnl_sol, realized_pnl, reason);

            Ok(pnl_sol)
        } else {
            Err(anyhow!("Position not found for token {}", token_mint))
        }
    }

    /// Update position prices and check for stop-loss triggers
    pub async fn update_positions(&self) -> Result<Vec<Pubkey>> {
        let config = self.config.read().await;
        let mut positions = self.positions.write().await;
        let mut positions_to_close = Vec::new();

        for (token_mint, position) in positions.iter_mut() {
            // Get current price from market data
            if let Ok(Some(price_data)) = self.market_data.get_price(token_mint).await {
                let current_price = price_data.price;
                position.update_price(current_price);

                // Update trailing stop
                if config.trailing_stop_percentage > 0.0 {
                    let trailing_stop_price = current_price * (1.0 - config.trailing_stop_percentage / 100.0);
                    if position.trailing_stop_price.is_none() ||
                       trailing_stop_price > position.trailing_stop_price.unwrap() {
                        position.trailing_stop_price = Some(trailing_stop_price);
                    }
                }

                // Check stop-loss conditions
                if position.should_stop_loss(config.stop_loss_percentage) {
                    positions_to_close.push(*token_mint);
                    println!("⚠️ Stop-loss triggered for {}: {:.2}% loss",
                        token_mint, position.unrealized_pnl);
                }

                // Check trailing stop
                if position.should_trailing_stop(config.trailing_stop_percentage) {
                    positions_to_close.push(*token_mint);
                    println!("📈 Trailing stop triggered for {}: protecting gains", token_mint);
                }

                // Check position timeout
                if position.is_expired(config.position_timeout_minutes) {
                    positions_to_close.push(*token_mint);
                    println!("⏰ Position timeout for {}: held too long", token_mint);
                }

                // Check emergency stop-loss
                if position.unrealized_pnl <= -config.emergency_stop_loss_percentage {
                    position.is_emergency_exit = true;
                    positions_to_close.push(*token_mint);
                    println!("🚨 EMERGENCY STOP-LOSS for {}: {:.2}% loss",
                        token_mint, position.unrealized_pnl);
                }
            }
        }

        Ok(positions_to_close)
    }

    /// Check and trigger circuit breaker if necessary
    pub async fn check_circuit_breaker(&self) -> Result<bool> {
        let config = self.config.read().await;
        let daily_stats = self.daily_stats.read().await;

        // Check daily loss limit
        let daily_loss_sol = self.daily_loss_tracker.load(Ordering::Acquire) as f64 / 1e9;
        if daily_loss_sol >= config.daily_loss_limit_sol {
            self.trigger_circuit_breaker("Daily loss limit exceeded").await?;
            return Ok(true);
        }

        // Check max drawdown
        if daily_stats.realized_pnl_sol <= -config.circuit_breaker_loss_threshold {
            self.trigger_circuit_breaker("Circuit breaker loss threshold exceeded").await?;
            return Ok(true);
        }

        // Check unrealized losses
        let total_unrealized_loss = self.calculate_total_unrealized_pnl().await;
        if total_unrealized_loss <= -config.circuit_breaker_loss_threshold {
            self.trigger_circuit_breaker("Unrealized loss threshold exceeded").await?;
            return Ok(true);
        }

        Ok(false)
    }

    /// Start the risk monitoring background task
    pub async fn start_monitoring(&self) -> Result<()> {
        let risk_manager = Arc::new(self.clone());

        // Start position monitoring task
        let position_monitor = risk_manager.clone();
        tokio::spawn(async move {
            position_monitor.position_monitoring_loop().await;
        });

        // Start circuit breaker monitoring task
        let circuit_breaker_monitor = risk_manager.clone();
        tokio::spawn(async move {
            circuit_breaker_monitor.circuit_breaker_monitoring_loop().await;
        });

        // Start daily reset task
        let daily_reset_monitor = risk_manager.clone();
        tokio::spawn(async move {
            daily_reset_monitor.daily_reset_loop().await;
        });

        // Start alert monitoring task
        let alert_monitor = risk_manager.clone();
        tokio::spawn(async move {
            alert_monitor.alert_monitoring_loop().await;
        });

        println!("🛡️ Risk monitoring tasks started");
        println!("   📊 Position monitoring: Every 30 seconds");
        println!("   🔴 Circuit breaker monitoring: Every 60 seconds");
        println!("   🚨 Alert monitoring: Every 10 seconds");
        println!("   📅 Daily reset monitoring: Every hour");
        Ok(())
    }

    /// Position monitoring background loop
    async fn position_monitoring_loop(&self) {
        let mut interval = tokio::time::interval(Duration::from_secs(30)); // Check every 30 seconds

        loop {
            interval.tick().await;

            // Update positions and check for stop-loss triggers
            match self.update_positions().await {
                Ok(positions_to_close) => {
                    for token_mint in positions_to_close {
                        // Get current price and close position
                        if let Ok(Some(price_data)) = self.market_data.get_price(&token_mint).await {
                            if let Err(e) = self.close_position(token_mint, price_data.price, "Stop-loss triggered").await {
                                println!("❌ Failed to close position {}: {}", token_mint, e);
                            }
                        }
                    }
                }
                Err(e) => {
                    println!("⚠️ Error updating positions: {}", e);
                }
            }
        }
    }

    /// Circuit breaker monitoring background loop
    async fn circuit_breaker_monitoring_loop(&self) {
        let mut interval = tokio::time::interval(Duration::from_secs(60)); // Check every minute

        loop {
            interval.tick().await;

            if let Err(e) = self.check_circuit_breaker().await {
                println!("⚠️ Error checking circuit breaker: {}", e);
            }
        }
    }

    /// Daily reset monitoring loop
    async fn daily_reset_loop(&self) {
        let mut last_reset_date = chrono::Utc::now().format("%Y-%m-%d").to_string();
        let mut interval = tokio::time::interval(Duration::from_secs(3600)); // Check every hour

        loop {
            interval.tick().await;

            let current_date = chrono::Utc::now().format("%Y-%m-%d").to_string();
            if current_date != last_reset_date {
                self.reset_daily_stats().await;
                last_reset_date = current_date;
            }
        }
    }

    /// Force close all positions (emergency liquidation)
    pub async fn emergency_liquidate_all(&self, reason: &str) -> Result<()> {
        println!("🚨 EMERGENCY LIQUIDATION: {}", reason);

        let positions_to_close: Vec<Pubkey> = {
            let positions = self.positions.read().await;
            positions.keys().cloned().collect()
        };

        let mut liquidation_results = Vec::new();

        for token_mint in positions_to_close {
            if let Ok(Some(price_data)) = self.market_data.get_price(&token_mint).await {
                match self.close_position(token_mint, price_data.price, "Emergency liquidation").await {
                    Ok(pnl) => {
                        liquidation_results.push((token_mint, pnl));
                        println!("✅ Liquidated position {}: {:.4} SOL P&L", token_mint, pnl);
                    }
                    Err(e) => {
                        println!("❌ Failed to liquidate position {}: {}", token_mint, e);
                    }
                }
            }
        }

        let total_pnl: f64 = liquidation_results.iter().map(|(_, pnl)| pnl).sum();
        println!("📊 Emergency liquidation complete. Total P&L: {:.4} SOL", total_pnl);

        Ok(())
    }

    /// Set custom stop-loss for a specific position
    pub async fn set_position_stop_loss(&self, token_mint: Pubkey, stop_loss_price: f64) -> Result<()> {
        let mut positions = self.positions.write().await;

        if let Some(position) = positions.get_mut(&token_mint) {
            position.stop_loss_price = Some(stop_loss_price);
            println!("📊 Stop-loss set for {}: {:.6}", token_mint, stop_loss_price);
            Ok(())
        } else {
            Err(anyhow!("Position not found for token {}", token_mint))
        }
    }

    /// Set custom trailing stop for a specific position
    pub async fn set_position_trailing_stop(&self, token_mint: Pubkey, trailing_stop_percentage: f64) -> Result<()> {
        let mut positions = self.positions.write().await;

        if let Some(position) = positions.get_mut(&token_mint) {
            let trailing_stop_price = position.current_price * (1.0 - trailing_stop_percentage / 100.0);
            position.trailing_stop_price = Some(trailing_stop_price);
            println!("📈 Trailing stop set for {}: {:.6} ({:.2}%)",
                token_mint, trailing_stop_price, trailing_stop_percentage);
            Ok(())
        } else {
            Err(anyhow!("Position not found for token {}", token_mint))
        }
    }

    /// Get all current positions
    pub async fn get_all_positions(&self) -> HashMap<Pubkey, Position> {
        let positions = self.positions.read().await;
        positions.clone()
    }

    /// Check if emergency stop is active
    pub fn is_emergency_stop_active(&self) -> bool {
        self.emergency_stop.load(Ordering::Acquire)
    }

    /// Check if circuit breaker is active
    pub fn is_circuit_breaker_active(&self) -> bool {
        self.circuit_breaker_active.load(Ordering::Acquire)
    }

    /// Get comprehensive risk monitoring report
    pub async fn get_risk_monitoring_report(&self) -> RiskMonitoringReport {
        let positions = self.positions.read().await;
        let daily_stats = self.daily_stats.read().await;
        let config = self.config.read().await;

        let total_exposure_sol = self.total_exposure.load(Ordering::Acquire) as f64 / 1e9;
        let daily_loss_sol = self.daily_loss_tracker.load(Ordering::Acquire) as f64 / 1e9;

        // Calculate portfolio metrics
        let total_unrealized_pnl = self.calculate_total_unrealized_pnl().await;
        let total_positions = positions.len();
        let positions_at_risk = positions.values()
            .filter(|p| p.unrealized_pnl < -config.stop_loss_percentage / 100.0 * p.amount_sol)
            .count();

        // Calculate risk scores
        let exposure_risk_score = (total_exposure_sol / config.max_position_size_sol).min(1.0);
        let loss_risk_score = (daily_loss_sol / config.daily_loss_limit_sol).min(1.0);
        let pnl_risk_score = if total_unrealized_pnl < 0.0 {
            (-total_unrealized_pnl / config.circuit_breaker_loss_threshold).min(1.0)
        } else {
            0.0
        };

        let overall_risk_score = (exposure_risk_score + loss_risk_score + pnl_risk_score) / 3.0;

        // Generate alerts
        let mut alerts = Vec::new();

        if exposure_risk_score > 0.8 {
            alerts.push(RiskAlert {
                severity: AlertSeverity::High,
                message: format!("High exposure risk: {:.1}% of max position size", exposure_risk_score * 100.0),
                timestamp: chrono::Utc::now().timestamp_millis() as u64,
                alert_type: RiskAlertType::ExposureLimit,
            });
        }

        if loss_risk_score > 0.7 {
            alerts.push(RiskAlert {
                severity: AlertSeverity::High,
                message: format!("Daily loss approaching limit: {:.1}% of daily limit", loss_risk_score * 100.0),
                timestamp: chrono::Utc::now().timestamp_millis() as u64,
                alert_type: RiskAlertType::LossLimit,
            });
        }

        if positions_at_risk > 0 {
            alerts.push(RiskAlert {
                severity: AlertSeverity::Medium,
                message: format!("{} positions approaching stop-loss", positions_at_risk),
                timestamp: chrono::Utc::now().timestamp_millis() as u64,
                alert_type: RiskAlertType::StopLoss,
            });
        }

        if self.is_emergency_stop_active() {
            alerts.push(RiskAlert {
                severity: AlertSeverity::Critical,
                message: "Emergency stop is active".to_string(),
                timestamp: chrono::Utc::now().timestamp_millis() as u64,
                alert_type: RiskAlertType::EmergencyStop,
            });
        }

        if self.is_circuit_breaker_active() {
            alerts.push(RiskAlert {
                severity: AlertSeverity::Critical,
                message: "Circuit breaker is active".to_string(),
                timestamp: chrono::Utc::now().timestamp_millis() as u64,
                alert_type: RiskAlertType::CircuitBreaker,
            });
        }

        RiskMonitoringReport {
            timestamp: chrono::Utc::now().timestamp_millis() as u64,
            overall_risk_score,
            exposure_risk_score,
            loss_risk_score,
            pnl_risk_score,
            total_exposure_sol,
            daily_loss_sol,
            total_unrealized_pnl,
            total_positions,
            positions_at_risk,
            emergency_stop_active: self.is_emergency_stop_active(),
            circuit_breaker_active: self.is_circuit_breaker_active(),
            alerts,
            daily_stats: daily_stats.clone(),
        }
    }

    /// Start real-time alert monitoring
    pub async fn start_alert_monitoring(&self) -> Result<()> {
        let risk_manager = Arc::new(self.clone());

        tokio::spawn(async move {
            risk_manager.alert_monitoring_loop().await;
        });

        println!("🚨 Real-time alert monitoring started");
        Ok(())
    }

    /// Alert monitoring background loop
    async fn alert_monitoring_loop(&self) {
        let mut interval = tokio::time::interval(Duration::from_secs(10)); // Check every 10 seconds

        loop {
            interval.tick().await;

            let report = self.get_risk_monitoring_report().await;

            // Process high-severity alerts
            for alert in report.alerts.iter().filter(|a| matches!(a.severity, AlertSeverity::High | AlertSeverity::Critical)) {
                self.process_alert(alert).await;
            }

            // Log overall risk status
            if report.overall_risk_score > 0.7 {
                println!("⚠️ HIGH RISK: Overall risk score: {:.1}%", report.overall_risk_score * 100.0);
            } else if report.overall_risk_score > 0.5 {
                println!("⚠️ MEDIUM RISK: Overall risk score: {:.1}%", report.overall_risk_score * 100.0);
            }
        }
    }

    /// Process individual alerts
    async fn process_alert(&self, alert: &RiskAlert) {
        match alert.severity {
            AlertSeverity::Critical => {
                println!("🚨 CRITICAL ALERT: {}", alert.message);
                // Could trigger additional actions like notifications, etc.
            }
            AlertSeverity::High => {
                println!("🔴 HIGH ALERT: {}", alert.message);
            }
            AlertSeverity::Medium => {
                println!("🟡 MEDIUM ALERT: {}", alert.message);
            }
            AlertSeverity::Low => {
                println!("🟢 LOW ALERT: {}", alert.message);
            }
        }
    }

    /// Get portfolio exposure breakdown
    pub async fn get_portfolio_exposure(&self) -> PortfolioExposure {
        let positions = self.positions.read().await;
        let config = self.config.read().await;

        let mut token_exposures = HashMap::new();
        let mut total_long_exposure = 0.0;
        let mut total_short_exposure = 0.0;

        for (token_mint, position) in positions.iter() {
            let exposure = token_exposures.entry(*token_mint).or_insert(0.0);
            *exposure += position.amount_sol;

            if position.amount_sol > 0.0 {
                total_long_exposure += position.amount_sol;
            } else {
                total_short_exposure += position.amount_sol.abs();
            }
        }

        let net_exposure = total_long_exposure - total_short_exposure;
        let gross_exposure = total_long_exposure + total_short_exposure;

        PortfolioExposure {
            total_long_exposure,
            total_short_exposure,
            net_exposure,
            gross_exposure,
            max_allowed_exposure: config.max_position_size_sol,
            exposure_utilization: gross_exposure / config.max_position_size_sol,
            token_exposures,
        }
    }

    /// Get detailed position analytics
    pub async fn get_position_analytics(&self) -> PositionAnalytics {
        let positions = self.positions.read().await;

        let total_positions = positions.len();
        let profitable_positions = positions.values().filter(|p| p.unrealized_pnl > 0.0).count();
        let losing_positions = positions.values().filter(|p| p.unrealized_pnl < 0.0).count();

        let total_unrealized_pnl: f64 = positions.values().map(|p| p.unrealized_pnl).sum();
        let average_position_size: f64 = if total_positions > 0 {
            positions.values().map(|p| p.amount_sol.abs()).sum::<f64>() / total_positions as f64
        } else {
            0.0
        };

        let largest_position = positions.values()
            .max_by(|a, b| a.amount_sol.abs().partial_cmp(&b.amount_sol.abs()).unwrap())
            .map(|p| p.amount_sol.abs())
            .unwrap_or(0.0);

        let win_rate = if total_positions > 0 {
            profitable_positions as f64 / total_positions as f64
        } else {
            0.0
        };

        PositionAnalytics {
            total_positions,
            profitable_positions,
            losing_positions,
            total_unrealized_pnl,
            average_position_size,
            largest_position,
            win_rate,
        }
    }

    /// Check market conditions for safe trading
    pub async fn check_market_conditions(&self, token_mint: Pubkey, trade_size_sol: f64) -> Result<MarketConditionCheck> {
        let config = self.config.read().await;

        // Get current market data
        let price_data = self.market_data.get_price(&token_mint).await?
            .ok_or_else(|| anyhow!("Price data not available for token {}", token_mint))?;

        let mut warnings = Vec::new();
        let mut blocking_issues = Vec::new();

        // 1. Volatility Check
        let volatility_check = self.check_volatility(&token_mint, &config).await?;
        if !volatility_check.is_safe {
            if volatility_check.is_critical {
                blocking_issues.push(format!("High volatility detected: {:.2}%", volatility_check.volatility_percentage));
            } else {
                warnings.push(format!("Elevated volatility: {:.2}%", volatility_check.volatility_percentage));
            }
        }

        // 2. Liquidity Depth Check
        let liquidity_check = self.check_liquidity_depth(&token_mint, trade_size_sol).await?;
        if !liquidity_check.is_sufficient {
            if liquidity_check.available_liquidity_sol < trade_size_sol * 0.5 {
                blocking_issues.push(format!("Insufficient liquidity: {:.4} SOL available, {:.4} SOL required",
                    liquidity_check.available_liquidity_sol, trade_size_sol));
            } else {
                warnings.push(format!("Limited liquidity: {:.4} SOL available for {:.4} SOL trade",
                    liquidity_check.available_liquidity_sol, trade_size_sol));
            }
        }

        // 3. Slippage Protection Check
        let slippage_check = self.check_slippage_protection(trade_size_sol, &liquidity_check).await?;
        if slippage_check.estimated_slippage > config.max_slippage_percentage {
            blocking_issues.push(format!("Excessive slippage: {:.2}% (max: {:.2}%)",
                slippage_check.estimated_slippage, config.max_slippage_percentage));
        } else if slippage_check.estimated_slippage > config.max_slippage_percentage * 0.8 {
            warnings.push(format!("High slippage: {:.2}%", slippage_check.estimated_slippage));
        }

        // 4. Trading Hours Check
        let trading_hours_check = self.check_trading_hours(&config).await;
        if !trading_hours_check.is_allowed {
            blocking_issues.push(trading_hours_check.reason.clone());
        }

        // 5. Market Stability Check
        let stability_check = self.check_market_stability(&token_mint).await?;
        if !stability_check.is_stable {
            if stability_check.is_critical {
                blocking_issues.push(format!("Market instability detected: {}", stability_check.reason));
            } else {
                warnings.push(format!("Market instability warning: {}", stability_check.reason));
            }
        }

        let is_safe_to_trade = blocking_issues.is_empty();
        let recommended_trade_size = if is_safe_to_trade {
            liquidity_check.recommended_trade_size.min(trade_size_sol)
        } else {
            0.0
        };

        Ok(MarketConditionCheck {
            is_safe_to_trade,
            warnings,
            blocking_issues,
            volatility_check: volatility_check.clone(),
            liquidity_check: liquidity_check.clone(),
            slippage_check,
            trading_hours_check,
            stability_check: stability_check.clone(),
            recommended_trade_size,
            confidence_score: self.calculate_market_confidence(&volatility_check, &liquidity_check, &stability_check),
        })
    }

    /// Check volatility conditions
    async fn check_volatility(&self, token_mint: &Pubkey, config: &RiskConfig) -> Result<VolatilityCheck> {
        // Get volatility data from market data manager
        if let Ok(Some(volatility_data)) = self.market_data.get_volatility_analysis(token_mint).await {
            let volatility_percentage = volatility_data.current_volatility * 100.0;

            let is_critical = volatility_percentage > config.max_volatility_percentage * 1.5;
            let is_safe = volatility_percentage <= config.max_volatility_percentage;

            Ok(VolatilityCheck {
                volatility_percentage,
                is_safe,
                is_critical,
                timeframe: "24h".to_string(),
            })
        } else {
            // Fallback: assume moderate volatility if data unavailable
            Ok(VolatilityCheck {
                volatility_percentage: 10.0, // Default 10% volatility
                is_safe: true,
                is_critical: false,
                timeframe: "estimated".to_string(),
            })
        }
    }

    /// Check liquidity depth
    async fn check_liquidity_depth(&self, token_mint: &Pubkey, trade_size_sol: f64) -> Result<LiquidityCheck> {
        // Get market depth from market data manager
        if let Ok(Some(depth_analysis)) = self.market_data.get_market_depth(token_mint).await {
            let available_liquidity_sol = depth_analysis.total_liquidity_a as f64 / 1e9; // Convert lamports to SOL
            let is_sufficient = available_liquidity_sol >= trade_size_sol * 2.0; // Require 2x liquidity
            let recommended_trade_size = (available_liquidity_sol * 0.3).min(trade_size_sol); // Use max 30% of liquidity

            Ok(LiquidityCheck {
                available_liquidity_sol,
                required_liquidity_sol: trade_size_sol,
                is_sufficient,
                recommended_trade_size,
                liquidity_ratio: available_liquidity_sol / trade_size_sol,
            })
        } else {
            // Fallback: assume limited liquidity
            Ok(LiquidityCheck {
                available_liquidity_sol: trade_size_sol * 1.5, // Assume 1.5x available
                required_liquidity_sol: trade_size_sol,
                is_sufficient: false,
                recommended_trade_size: trade_size_sol * 0.5,
                liquidity_ratio: 1.5,
            })
        }
    }

    /// Check slippage protection
    async fn check_slippage_protection(&self, trade_size_sol: f64, liquidity_check: &LiquidityCheck) -> Result<SlippageCheck> {
        // Estimate slippage based on trade size vs liquidity
        let liquidity_impact = trade_size_sol / liquidity_check.available_liquidity_sol;
        let estimated_slippage = liquidity_impact * 100.0; // Simple linear model

        // Apply curve for more realistic slippage estimation
        let adjusted_slippage = if liquidity_impact < 0.1 {
            estimated_slippage * 0.5 // Low impact
        } else if liquidity_impact < 0.3 {
            estimated_slippage * 1.0 // Medium impact
        } else {
            estimated_slippage * 2.0 // High impact
        };

        Ok(SlippageCheck {
            estimated_slippage: adjusted_slippage,
            liquidity_impact,
            is_acceptable: adjusted_slippage <= 5.0, // Max 5% slippage
        })
    }

    /// Check trading hours
    async fn check_trading_hours(&self, config: &RiskConfig) -> TradingHoursCheck {
        if !config.enforce_trading_hours {
            return TradingHoursCheck {
                is_allowed: true,
                reason: "Trading hours not enforced".to_string(),
            };
        }

        let now = chrono::Utc::now();
        let hour = now.hour();

        // Example: Only trade during active market hours (adjust as needed)
        if hour >= 6 && hour <= 22 { // 6 AM to 10 PM UTC
            TradingHoursCheck {
                is_allowed: true,
                reason: "Within trading hours".to_string(),
            }
        } else {
            TradingHoursCheck {
                is_allowed: false,
                reason: format!("Outside trading hours (current: {}:00 UTC)", hour),
            }
        }
    }

    /// Check market stability
    async fn check_market_stability(&self, token_mint: &Pubkey) -> Result<StabilityCheck> {
        // Check for recent price spikes or drops
        if let Ok(Some(price_data)) = self.market_data.get_price(token_mint).await {
            // Simple stability check based on confidence score
            let is_stable = price_data.confidence >= 0.8;
            let is_critical = price_data.confidence < 0.5;

            let reason = if is_critical {
                "Low price confidence detected".to_string()
            } else if !is_stable {
                "Moderate price instability".to_string()
            } else {
                "Market appears stable".to_string()
            };

            Ok(StabilityCheck {
                is_stable,
                is_critical,
                reason,
                confidence_score: price_data.confidence,
            })
        } else {
            Ok(StabilityCheck {
                is_stable: false,
                is_critical: true,
                reason: "Price data unavailable".to_string(),
                confidence_score: 0.0,
            })
        }
    }

    /// Calculate overall market confidence
    fn calculate_market_confidence(&self, volatility: &VolatilityCheck, liquidity: &LiquidityCheck, stability: &StabilityCheck) -> f64 {
        let volatility_score = if volatility.is_safe { 1.0 } else if volatility.is_critical { 0.0 } else { 0.5 };
        let liquidity_score = if liquidity.is_sufficient { 1.0 } else { liquidity.liquidity_ratio.min(1.0) };
        let stability_score = stability.confidence_score;

        (volatility_score + liquidity_score + stability_score) / 3.0
    }

    /// Validate transaction safety before execution
    pub async fn validate_transaction_safety(
        &self,
        token_mint: Pubkey,
        trade_amount_sol: f64,
        expected_profit_sol: f64,
        max_gas_fee_sol: f64,
    ) -> Result<TransactionValidation> {
        let config = self.config.read().await;
        let mut warnings = Vec::new();
        let mut blocking_issues = Vec::new();

        // 1. Pre-execution safety checks
        let pre_execution_check = self.pre_execution_safety_check(
            token_mint,
            trade_amount_sol,
            expected_profit_sol,
            &config,
        ).await?;

        if !pre_execution_check.is_safe {
            blocking_issues.extend(pre_execution_check.issues);
        }
        warnings.extend(pre_execution_check.warnings);

        // 2. Gas fee protection
        let gas_fee_check = self.validate_gas_fees(max_gas_fee_sol, expected_profit_sol, &config).await?;
        if !gas_fee_check.is_acceptable {
            if gas_fee_check.is_critical {
                blocking_issues.push(gas_fee_check.reason);
            } else {
                warnings.push(gas_fee_check.reason);
            }
        }

        // 3. Transaction size validation
        let size_check = self.validate_transaction_size(trade_amount_sol, &config).await?;
        if !size_check.is_valid {
            blocking_issues.extend(size_check.issues);
        }
        warnings.extend(size_check.warnings);

        // 4. Risk exposure validation
        let exposure_check = self.validate_risk_exposure(token_mint, trade_amount_sol).await?;
        if !exposure_check.is_acceptable {
            if exposure_check.would_exceed_limits {
                blocking_issues.push(exposure_check.reason);
            } else {
                warnings.push(exposure_check.reason);
            }
        }

        // 5. Market timing validation
        let timing_check = self.validate_market_timing().await?;
        if !timing_check.is_good_timing {
            if timing_check.is_critical {
                blocking_issues.push(timing_check.reason);
            } else {
                warnings.push(timing_check.reason);
            }
        }

        let is_valid = blocking_issues.is_empty();
        let recommended_gas_limit = self.calculate_recommended_gas_limit(trade_amount_sol, &config).await;

        Ok(TransactionValidation {
            is_valid,
            warnings,
            blocking_issues,
            recommended_gas_limit,
        })
    }

    /// Pre-execution safety check
    async fn pre_execution_safety_check(
        &self,
        token_mint: Pubkey,
        trade_amount_sol: f64,
        expected_profit_sol: f64,
        config: &RiskConfig,
    ) -> Result<PreExecutionCheck> {
        let mut issues = Vec::new();
        let mut warnings = Vec::new();

        // Check if trading is currently allowed
        if !self.is_trading_allowed().await {
            issues.push("Trading is currently disabled by risk management".to_string());
        }

        // Check emergency stop status
        if self.emergency_stop.load(std::sync::atomic::Ordering::Relaxed) {
            issues.push("Emergency stop is active".to_string());
        }

        // Check circuit breaker status
        if self.circuit_breaker_active.load(std::sync::atomic::Ordering::Relaxed) {
            issues.push("Circuit breaker is active".to_string());
        }

        // Check profit expectations
        if expected_profit_sol <= 0.0 {
            issues.push("Expected profit must be positive".to_string());
        } else if expected_profit_sol < config.min_profit_threshold_sol {
            warnings.push(format!("Low expected profit: {:.6} SOL", expected_profit_sol));
        }

        // Check trade amount limits
        if trade_amount_sol > config.max_single_trade_sol {
            issues.push(format!("Trade amount {:.6} SOL exceeds maximum {:.6} SOL",
                trade_amount_sol, config.max_single_trade_sol));
        }

        // Check daily limits
        let daily_stats = self.daily_stats.read().await;
        if daily_stats.trade_count >= config.daily_trade_limit {
            issues.push("Daily trade limit reached".to_string());
        }

        if daily_stats.volume_sol + trade_amount_sol > config.daily_volume_limit_sol {
            issues.push("Trade would exceed daily volume limit".to_string());
        }

        let is_safe = issues.is_empty();

        Ok(PreExecutionCheck {
            is_safe,
            issues,
            warnings,
        })
    }

    /// Validate gas fees
    async fn validate_gas_fees(
        &self,
        max_gas_fee_sol: f64,
        expected_profit_sol: f64,
        config: &RiskConfig,
    ) -> Result<GasFeeCheck> {
        let gas_to_profit_ratio = max_gas_fee_sol / expected_profit_sol;

        let is_critical = gas_to_profit_ratio > 0.8; // Gas fee > 80% of profit
        let is_acceptable = gas_to_profit_ratio <= config.max_gas_to_profit_ratio;

        let reason = if is_critical {
            format!("Gas fee {:.6} SOL is {:.1}% of expected profit {:.6} SOL",
                max_gas_fee_sol, gas_to_profit_ratio * 100.0, expected_profit_sol)
        } else if !is_acceptable {
            format!("High gas fee ratio: {:.1}% of profit", gas_to_profit_ratio * 100.0)
        } else {
            "Gas fees acceptable".to_string()
        };

        Ok(GasFeeCheck {
            is_acceptable,
            is_critical,
            gas_to_profit_ratio,
            reason,
        })
    }

    /// Validate transaction size
    async fn validate_transaction_size(
        &self,
        trade_amount_sol: f64,
        config: &RiskConfig,
    ) -> Result<TransactionSizeCheck> {
        let mut issues = Vec::new();
        let mut warnings = Vec::new();

        if trade_amount_sol <= 0.0 {
            issues.push("Trade amount must be positive".to_string());
        }

        if trade_amount_sol < config.min_trade_size_sol {
            warnings.push(format!("Small trade size: {:.6} SOL", trade_amount_sol));
        }

        if trade_amount_sol > config.max_single_trade_sol {
            issues.push(format!("Trade size {:.6} SOL exceeds maximum {:.6} SOL",
                trade_amount_sol, config.max_single_trade_sol));
        }

        let is_valid = issues.is_empty();

        Ok(TransactionSizeCheck {
            is_valid,
            issues,
            warnings,
        })
    }

    /// Validate risk exposure
    async fn validate_risk_exposure(
        &self,
        token_mint: Pubkey,
        trade_amount_sol: f64,
    ) -> Result<RiskExposureCheck> {
        let config = self.config.read().await;
        let positions = self.positions.read().await;

        // Calculate current exposure
        let current_total_exposure: f64 = positions.values()
            .map(|p| p.amount_sol.abs())
            .sum();

        let current_token_exposure: f64 = positions.values()
            .filter(|p| p.token_mint == token_mint)
            .map(|p| p.amount_sol.abs())
            .sum();

        let new_total_exposure = current_total_exposure + trade_amount_sol;
        let new_token_exposure = current_token_exposure + trade_amount_sol;

        let would_exceed_total = new_total_exposure > config.max_total_exposure_sol;
        let would_exceed_token = new_token_exposure > config.max_position_size_sol;

        let would_exceed_limits = would_exceed_total || would_exceed_token;
        let is_acceptable = !would_exceed_limits;

        let reason = if would_exceed_total {
            format!("Would exceed total exposure limit: {:.6} SOL > {:.6} SOL",
                new_total_exposure, config.max_total_exposure_sol)
        } else if would_exceed_token {
            format!("Would exceed token exposure limit: {:.6} SOL > {:.6} SOL",
                new_token_exposure, config.max_position_size_sol)
        } else {
            "Exposure within limits".to_string()
        };

        Ok(RiskExposureCheck {
            is_acceptable,
            would_exceed_limits,
            current_total_exposure,
            new_total_exposure,
            current_token_exposure,
            new_token_exposure,
            reason,
        })
    }

    /// Validate market timing
    async fn validate_market_timing(&self) -> Result<MarketTimingCheck> {
        let config = self.config.read().await;

        // Check if we're in a cool-down period
        let last_trade_time = self.last_trade_timestamp.load(std::sync::atomic::Ordering::Relaxed);
        let now = chrono::Utc::now().timestamp_millis() as u64;
        let time_since_last_trade = now - last_trade_time;

        let in_cooldown = time_since_last_trade < config.cool_down_period_seconds * 1000;

        let is_critical = in_cooldown && config.enforce_cool_down;
        let is_good_timing = !is_critical;

        let reason = if is_critical {
            let remaining_cooldown = (config.cool_down_period_seconds * 1000 - time_since_last_trade) / 1000;
            format!("In cool-down period: {} seconds remaining", remaining_cooldown)
        } else {
            "Market timing acceptable".to_string()
        };

        Ok(MarketTimingCheck {
            is_good_timing,
            is_critical,
            in_cooldown,
            time_since_last_trade_ms: time_since_last_trade,
            reason,
        })
    }

    /// Calculate recommended gas limit
    async fn calculate_recommended_gas_limit(&self, trade_amount_sol: f64, config: &RiskConfig) -> u64 {
        // Base gas limit for arbitrage transactions
        let base_gas = 200_000u64;

        // Scale with trade size
        let size_multiplier = (trade_amount_sol / config.max_single_trade_sol).min(2.0);
        let scaled_gas = (base_gas as f64 * (1.0 + size_multiplier)) as u64;

        // Add safety margin
        (scaled_gas as f64 * 1.2) as u64 // 20% safety margin
    }

    /// Post-execution verification
    pub async fn verify_transaction_execution(
        &self,
        transaction_signature: String,
        expected_profit_sol: f64,
        actual_profit_sol: f64,
        gas_used_sol: f64,
    ) -> Result<ExecutionVerification> {
        let mut issues = Vec::new();
        let mut warnings = Vec::new();

        // Check if transaction was profitable
        let net_profit = actual_profit_sol - gas_used_sol;
        if net_profit <= 0.0 {
            issues.push(format!("Transaction was not profitable: net {:.6} SOL", net_profit));
        }

        // Check profit vs expectations
        let profit_deviation = (actual_profit_sol - expected_profit_sol) / expected_profit_sol;
        if profit_deviation < -0.5 {
            warnings.push(format!("Profit significantly lower than expected: {:.1}% deviation",
                profit_deviation * 100.0));
        }

        // Check gas usage
        let config = self.config.read().await;
        let gas_to_profit_ratio = gas_used_sol / actual_profit_sol;
        if gas_to_profit_ratio > config.max_gas_to_profit_ratio {
            warnings.push(format!("High gas usage: {:.1}% of profit", gas_to_profit_ratio * 100.0));
        }

        let is_successful = issues.is_empty();

        // Update statistics
        if is_successful {
            self.update_successful_trade_stats(actual_profit_sol, gas_used_sol).await;
        } else {
            self.update_failed_trade_stats(gas_used_sol).await;
        }

        Ok(ExecutionVerification {
            is_successful,
            transaction_signature,
            expected_profit_sol,
            actual_profit_sol,
            gas_used_sol,
            net_profit_sol: net_profit,
            profit_deviation_percentage: profit_deviation * 100.0,
            issues,
            warnings,
        })
    }

    /// Update statistics for successful trades
    async fn update_successful_trade_stats(&self, profit_sol: f64, gas_sol: f64) {
        let mut daily_stats = self.daily_stats.write().await;
        daily_stats.successful_trades += 1;
        daily_stats.total_profit_sol += profit_sol;
        daily_stats.total_gas_sol += gas_sol;
    }

    /// Update statistics for failed trades
    async fn update_failed_trade_stats(&self, gas_sol: f64) {
        let mut daily_stats = self.daily_stats.write().await;
        daily_stats.failed_trades += 1;
        daily_stats.total_gas_sol += gas_sol;
    }

    /// Trigger emergency circuit breaker
    pub async fn trigger_circuit_breaker(&self, reason: &str) -> Result<()> {
        println!("🚨 CIRCUIT BREAKER ACTIVATED: {}", reason);

        self.circuit_breaker_active.store(true, Ordering::Release);
        self.emergency_stop.store(true, Ordering::Release);

        // Close all positions immediately
        let positions_to_close: Vec<Pubkey> = {
            let positions = self.positions.read().await;
            positions.keys().cloned().collect()
        };

        for token_mint in positions_to_close {
            if let Ok(Some(price_data)) = self.market_data.get_price(&token_mint).await {
                let _ = self.close_position(token_mint, price_data.price, "Circuit breaker").await;
            }
        }

        Ok(())
    }

    /// Calculate recommended position size based on risk
    async fn calculate_recommended_position_size(
        &self,
        requested_amount: f64,
        risk_score: f64,
        config: &RiskConfig,
    ) -> f64 {
        // Base position size adjustment based on risk
        let risk_adjustment = 1.0 - (risk_score * 0.5);
        let adjusted_amount = requested_amount * risk_adjustment;

        // Ensure we don't exceed limits
        let max_allowed = config.max_single_trade_sol.min(
            config.max_total_exposure_sol - (self.total_exposure.load(Ordering::Acquire) as f64 / 1e9)
        );

        adjusted_amount.min(max_allowed).max(0.0)
    }

    /// Check if current time is within trading hours
    async fn is_within_trading_hours(&self) -> bool {
        let config = self.config.read().await;

        // If start == end, assume 24/7 trading
        if config.trading_hours_start == config.trading_hours_end {
            return true;
        }

        let now = chrono::Utc::now();
        let current_hour = now.hour();

        if config.trading_hours_start <= config.trading_hours_end {
            // Normal hours (e.g., 9 to 17)
            current_hour >= config.trading_hours_start && current_hour <= config.trading_hours_end
        } else {
            // Overnight hours (e.g., 22 to 6)
            current_hour >= config.trading_hours_start || current_hour <= config.trading_hours_end
        }
    }

    /// Check daily trading limits
    async fn check_daily_limits(&self) -> bool {
        let config = self.config.read().await;
        let daily_stats = self.daily_stats.read().await;

        // Check trade count limit
        if daily_stats.total_trades >= config.daily_trade_limit {
            return false;
        }

        // Check volume limit
        if daily_stats.total_volume_sol >= config.daily_volume_limit_sol {
            return false;
        }

        // Check loss limit
        let daily_loss_sol = self.daily_loss_tracker.load(Ordering::Acquire) as f64 / 1e9;
        if daily_loss_sol >= config.daily_loss_limit_sol {
            return false;
        }

        true
    }

    /// Calculate total unrealized P&L across all positions
    async fn calculate_total_unrealized_pnl(&self) -> f64 {
        let positions = self.positions.read().await;
        positions.values()
            .map(|p| p.amount_sol * p.unrealized_pnl / 100.0)
            .sum()
    }

    /// Get current risk metrics
    pub async fn get_risk_metrics(&self) -> RiskMetrics {
        let config = self.config.read().await;
        let positions = self.positions.read().await;
        let daily_stats = self.daily_stats.read().await;

        let total_exposure_sol = self.total_exposure.load(Ordering::Acquire) as f64 / 1e9;
        let daily_loss_sol = self.daily_loss_tracker.load(Ordering::Acquire) as f64 / 1e9;
        let total_unrealized_pnl = positions.values()
            .map(|p| p.amount_sol * p.unrealized_pnl / 100.0)
            .sum();

        RiskMetrics {
            total_positions: positions.len(),
            total_exposure_sol,
            total_unrealized_pnl,
            daily_trades: daily_stats.total_trades,
            daily_volume_sol: daily_stats.total_volume_sol,
            daily_realized_pnl: daily_stats.realized_pnl_sol,
            daily_loss_sol,
            emergency_stop_active: self.emergency_stop.load(Ordering::Acquire),
            circuit_breaker_active: self.circuit_breaker_active.load(Ordering::Acquire),
            exposure_utilization: total_exposure_sol / config.max_total_exposure_sol,
            daily_loss_utilization: daily_loss_sol / config.daily_loss_limit_sol,
        }
    }

    /// Reset daily statistics (should be called at start of each day)
    pub async fn reset_daily_stats(&self) {
        let mut daily_stats = self.daily_stats.write().await;
        let today = chrono::Utc::now().format("%Y-%m-%d").to_string();

        *daily_stats = DailyStats {
            date: today,
            ..Default::default()
        };

        self.daily_loss_tracker.store(0, Ordering::Release);

        println!("📊 Daily risk statistics reset");
    }

    /// Manually trigger emergency stop
    pub async fn trigger_emergency_stop(&self, reason: &str) -> Result<()> {
        println!("🚨 EMERGENCY STOP TRIGGERED: {}", reason);
        self.emergency_stop.store(true, Ordering::Release);

        // Close all positions
        let positions_to_close: Vec<Pubkey> = {
            let positions = self.positions.read().await;
            positions.keys().cloned().collect()
        };

        for token_mint in positions_to_close {
            if let Ok(Some(price_data)) = self.market_data.get_price(&token_mint).await {
                let _ = self.close_position(token_mint, price_data.price, "Emergency stop").await;
            }
        }

        Ok(())
    }

    /// Resume trading after emergency stop (manual override)
    pub async fn resume_trading(&self, override_reason: &str) -> Result<()> {
        println!("✅ Trading resumed: {}", override_reason);
        self.emergency_stop.store(false, Ordering::Release);
        self.circuit_breaker_active.store(false, Ordering::Release);
        Ok(())
    }

    /// Validate a transaction before execution
    pub async fn validate_transaction(
        &self,
        token_mint: Pubkey,
        amount_sol: f64,
        expected_slippage: f64,
        gas_fee_sol: f64,
    ) -> Result<TransactionValidation> {
        let config = self.config.read().await;
        let mut warnings = Vec::new();
        let mut blocking_issues = Vec::new();

        // Check if trading is allowed
        if !self.is_trading_allowed().await {
            blocking_issues.push("Trading is currently disabled".to_string());
        }

        // Check gas fee reasonableness
        if gas_fee_sol > amount_sol * 0.1 {
            warnings.push(format!(
                "High gas fee: {:.6} SOL ({:.1}% of trade)",
                gas_fee_sol, gas_fee_sol / amount_sol * 100.0
            ));
        }

        // Check cool-down period
        let last_trade = self.last_trade_timestamp.load(Ordering::Acquire);
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        if now.saturating_sub(last_trade) < config.cool_down_period_seconds {
            blocking_issues.push("Cool-down period not elapsed".to_string());
        }

        Ok(TransactionValidation {
            is_valid: blocking_issues.is_empty(),
            warnings,
            blocking_issues,
            recommended_gas_limit: self.calculate_recommended_gas_limit(amount_sol, &config).await,
        })
    }



    /// Update last trade timestamp
    pub fn update_last_trade_timestamp(&self) {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        self.last_trade_timestamp.store(now, Ordering::Release);
    }
}

/// Risk metrics for monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskMetrics {
    pub total_positions: usize,
    pub total_exposure_sol: f64,
    pub total_unrealized_pnl: f64,
    pub daily_trades: u32,
    pub daily_volume_sol: f64,
    pub daily_realized_pnl: f64,
    pub daily_loss_sol: f64,
    pub emergency_stop_active: bool,
    pub circuit_breaker_active: bool,
    pub exposure_utilization: f64, // 0.0 to 1.0
    pub daily_loss_utilization: f64, // 0.0 to 1.0
}

/// Transaction validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionValidation {
    pub is_valid: bool,
    pub warnings: Vec<String>,
    pub blocking_issues: Vec<String>,
    pub recommended_gas_limit: u64,
}

/// Comprehensive risk monitoring report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskMonitoringReport {
    pub timestamp: u64,
    pub overall_risk_score: f64,
    pub exposure_risk_score: f64,
    pub loss_risk_score: f64,
    pub pnl_risk_score: f64,
    pub total_exposure_sol: f64,
    pub daily_loss_sol: f64,
    pub total_unrealized_pnl: f64,
    pub total_positions: usize,
    pub positions_at_risk: usize,
    pub emergency_stop_active: bool,
    pub circuit_breaker_active: bool,
    pub alerts: Vec<RiskAlert>,
    pub daily_stats: DailyStats,
}

/// Risk alert structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAlert {
    pub severity: AlertSeverity,
    pub message: String,
    pub timestamp: u64,
    pub alert_type: RiskAlertType,
}

/// Alert severity levels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Risk alert types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskAlertType {
    ExposureLimit,
    LossLimit,
    StopLoss,
    EmergencyStop,
    CircuitBreaker,
    PositionTimeout,
    VolatilitySpike,
}

/// Portfolio exposure breakdown
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioExposure {
    pub total_long_exposure: f64,
    pub total_short_exposure: f64,
    pub net_exposure: f64,
    pub gross_exposure: f64,
    pub max_allowed_exposure: f64,
    pub exposure_utilization: f64,
    pub token_exposures: HashMap<Pubkey, f64>,
}

/// Position analytics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PositionAnalytics {
    pub total_positions: usize,
    pub profitable_positions: usize,
    pub losing_positions: usize,
    pub total_unrealized_pnl: f64,
    pub average_position_size: f64,
    pub largest_position: f64,
    pub win_rate: f64,
}

/// Market condition check result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketConditionCheck {
    pub is_safe_to_trade: bool,
    pub warnings: Vec<String>,
    pub blocking_issues: Vec<String>,
    pub volatility_check: VolatilityCheck,
    pub liquidity_check: LiquidityCheck,
    pub slippage_check: SlippageCheck,
    pub trading_hours_check: TradingHoursCheck,
    pub stability_check: StabilityCheck,
    pub recommended_trade_size: f64,
    pub confidence_score: f64,
}

/// Volatility check result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VolatilityCheck {
    pub volatility_percentage: f64,
    pub is_safe: bool,
    pub is_critical: bool,
    pub timeframe: String,
}

/// Liquidity check result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiquidityCheck {
    pub available_liquidity_sol: f64,
    pub required_liquidity_sol: f64,
    pub is_sufficient: bool,
    pub recommended_trade_size: f64,
    pub liquidity_ratio: f64,
}

/// Slippage check result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlippageCheck {
    pub estimated_slippage: f64,
    pub liquidity_impact: f64,
    pub is_acceptable: bool,
}

/// Trading hours check result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradingHoursCheck {
    pub is_allowed: bool,
    pub reason: String,
}

/// Market stability check result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StabilityCheck {
    pub is_stable: bool,
    pub is_critical: bool,
    pub reason: String,
    pub confidence_score: f64,
}

/// Pre-execution safety check result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PreExecutionCheck {
    pub is_safe: bool,
    pub issues: Vec<String>,
    pub warnings: Vec<String>,
}

/// Gas fee validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GasFeeCheck {
    pub is_acceptable: bool,
    pub is_critical: bool,
    pub gas_to_profit_ratio: f64,
    pub reason: String,
}

/// Transaction size validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionSizeCheck {
    pub is_valid: bool,
    pub issues: Vec<String>,
    pub warnings: Vec<String>,
}

/// Risk exposure validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskExposureCheck {
    pub is_acceptable: bool,
    pub would_exceed_limits: bool,
    pub current_total_exposure: f64,
    pub new_total_exposure: f64,
    pub current_token_exposure: f64,
    pub new_token_exposure: f64,
    pub reason: String,
}

/// Market timing validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketTimingCheck {
    pub is_good_timing: bool,
    pub is_critical: bool,
    pub in_cooldown: bool,
    pub time_since_last_trade_ms: u64,
    pub reason: String,
}

/// Post-execution verification result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionVerification {
    pub is_successful: bool,
    pub transaction_signature: String,
    pub expected_profit_sol: f64,
    pub actual_profit_sol: f64,
    pub gas_used_sol: f64,
    pub net_profit_sol: f64,
    pub profit_deviation_percentage: f64,
    pub issues: Vec<String>,
    pub warnings: Vec<String>,
}
