use anyhow::Result;
use dotenv::dotenv;
use std::env;

#[tokio::main]
async fn main() -> Result<()> {
    // Load environment variables from .env file
    dotenv().ok();
    
    println!("🔧 Testing Configuration Setup...\n");
    
    // Test basic environment variable loading
    test_env_vars()?;
    
    // Test API connectivity
    test_api_connectivity().await?;
    
    println!("✅ All configuration tests passed!");
    
    Ok(())
}

fn test_env_vars() -> Result<()> {
    println!("📋 Testing Environment Variables:");
    
    // Test RPC configuration
    match env::var("RPC_HTTP") {
        Ok(rpc_url) => {
            let masked_url = mask_sensitive_info(&rpc_url);
            println!("  ✅ RPC_HTTP: {}", masked_url);
        }
        Err(_) => {
            println!("  ❌ RPC_HTTP not set");
            return Err(anyhow::anyhow!("RPC_HTTP environment variable not set"));
        }
    }
    
    // Test Birdeye API key
    match env::var("BIRDEYE_API_KEY") {
        Ok(api_key) => {
            let masked_key = mask_sensitive_info(&api_key);
            println!("  ✅ BIRDEYE_API_KEY: {}", masked_key);
        }
        Err(_) => {
            println!("  ❌ BIRDEYE_API_KEY not set");
            return Err(anyhow::anyhow!("BIRDEYE_API_KEY environment variable not set"));
        }
    }
    
    // Test performance settings
    let rpc_calls = env::var("MAX_CONCURRENT_RPC_CALLS").unwrap_or_else(|_| "15".to_string());
    let swaps = env::var("MAX_CONCURRENT_SWAPS").unwrap_or_else(|_| "3".to_string());
    println!("  ✅ Rate Limits: {} RPC calls, {} swaps", rpc_calls, swaps);
    
    // Test circuit breaker settings
    let failure_threshold = env::var("CIRCUIT_BREAKER_FAILURE_THRESHOLD").unwrap_or_else(|_| "5".to_string());
    println!("  ✅ Circuit Breaker: {} failure threshold", failure_threshold);
    
    println!();
    Ok(())
}

async fn test_api_connectivity() -> Result<()> {
    println!("🌐 Testing API Connectivity:");
    
    // Test RPC endpoint
    match test_rpc_connection().await {
        Ok(_) => println!("  ✅ RPC endpoint connection successful"),
        Err(e) => {
            println!("  ❌ RPC endpoint connection failed: {}", e);
            return Err(e);
        }
    }
    
    // Test Birdeye API
    match test_birdeye_api().await {
        Ok(_) => println!("  ✅ Birdeye API connection successful"),
        Err(e) => {
            println!("  ❌ Birdeye API connection failed: {}", e);
            return Err(e);
        }
    }
    
    println!();
    Ok(())
}

async fn test_rpc_connection() -> Result<()> {
    use anchor_client::solana_client::nonblocking::rpc_client::RpcClient;
    
    let rpc_url = env::var("RPC_HTTP")?;
    let client = RpcClient::new(rpc_url);
    
    // Test basic RPC call
    let _version = client.get_version().await
        .map_err(|e| anyhow::anyhow!("Failed to get RPC version: {}", e))?;
    
    Ok(())
}

async fn test_birdeye_api() -> Result<()> {
    use reqwest::Client;
    
    let api_key = env::var("BIRDEYE_API_KEY")?;
    let client = Client::new();
    
    // Test Birdeye API with SOL price endpoint
    let url = "https://public-api.birdeye.so/defi/price";
    let sol_mint = "So11111111111111111111111111111111111111112";
    
    let response = client
        .get(url)
        .header("X-API-KEY", &api_key)
        .query(&[("address", sol_mint)])
        .send()
        .await
        .map_err(|e| anyhow::anyhow!("Failed to call Birdeye API: {}", e))?;
    
    if !response.status().is_success() {
        return Err(anyhow::anyhow!(
            "Birdeye API returned error status: {}",
            response.status()
        ));
    }
    
    let _body: serde_json::Value = response
        .json()
        .await
        .map_err(|e| anyhow::anyhow!("Failed to parse Birdeye API response: {}", e))?;
    
    Ok(())
}

fn mask_sensitive_info(input: &str) -> String {
    if input.contains("api-key=") {
        let parts: Vec<&str> = input.split("api-key=").collect();
        if parts.len() == 2 {
            let key_part = parts[1];
            let masked_key = if key_part.len() > 8 {
                format!("{}...{}", &key_part[..4], &key_part[key_part.len()-4..])
            } else {
                "****".to_string()
            };
            return format!("{}api-key={}", parts[0], masked_key);
        }
    } else if input.len() > 8 {
        // For standalone API keys
        return format!("{}...{}", &input[..4], &input[input.len()-4..]);
    }
    
    "****".to_string()
}
