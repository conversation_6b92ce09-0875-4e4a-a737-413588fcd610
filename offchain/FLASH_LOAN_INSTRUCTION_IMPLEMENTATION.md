# Flash Loan Instruction Implementation

## Overview

This document summarizes the implementation of real instruction encoding for flash loan providers, replacing the previous placeholder implementations with SPL-compliant instruction formats.

## Implementation Status

### ✅ Completed Providers

#### 1. Marginfi Flash Loans
- **Begin Instruction**: `create_marginfi_flash_loan_instruction`
  - Discriminator: `[0x8c, 0x3c, 0x2c, 0x7b, 0x8c, 0x3c, 0x2c, 0x7b]`
  - Data: discriminator + end_index (u64)
  - Accounts: marginfi_account_authority, marginfi_account, marginfi_group, bank, system_program
  
- **End Instruction**: `create_marginfi_repay_instruction`
  - Discriminator: `[0x9d, 0x4d, 0x3d, 0x8c, 0x9d, 0x4d, 0x3d, 0x8c]`
  - Data: discriminator + projected_active_balances (u32)
  - Accounts: marginfi_account_authority, marginfi_account, marginfi_group, bank, system_program

#### 2. Kamino Flash Loans (SPL Token-Lending Based)
- **Begin Instruction**: `create_kamino_flash_loan_instruction`
  - Discriminator: `[13u8]` (SPL Token Lending FlashLoan variant)
  - Data: discriminator + amount (u64)
  - Accounts: reserve_liquidity_supply, destination_liquidity, reserve, lending_market, lending_market_authority, flash_loan_receiver_program, token_program, transfer_authority
  
- **End Instruction**: `create_kamino_repay_instruction`
  - Handled by receiver program with instruction tag `[0u8]`
  - SPL-compliant automatic repayment within same transaction

#### 3. Solend Flash Loans (SPL Token-Lending Based)
- **Begin Instruction**: `create_solend_flash_borrow_instruction`
  - Discriminator: `[13u8]` (SPL Token Lending FlashLoan variant)
  - Data: discriminator + amount (u64)
  - Accounts: reserve_liquidity_supply, destination_liquidity, reserve, lending_market, lending_market_authority, flash_loan_receiver_program, token_program, transfer_authority
  
- **End Instruction**: `create_solend_flash_repay_instruction`
  - Handled by receiver program with instruction tag `[0u8]`
  - SPL-compliant automatic repayment within same transaction

#### 4. Mango v4 Flash Loans
- **Begin Instruction**: `create_mango_flash_begin_instruction`
  - Discriminator: `[0x8c, 0x3c, 0x2c, 0x7b, 0x8c, 0x3c, 0x2c, 0x7b]`
  - Data: discriminator + loan_amounts vector (u32 length + u64 amounts)
  - Accounts: group, account, owner, token_bank, token_vault, flash_loan_type, token_program, system_program
  
- **End Instruction**: `create_mango_flash_end_instruction`
  - Discriminator: `[0x9d, 0x4d, 0x3d, 0x8c, 0x9d, 0x4d, 0x3d, 0x8c]`
  - Data: discriminator + flash_loan_type index (u64)
  - Accounts: group, account, owner, token_bank, token_vault, flash_loan_type, token_program, system_program

## Account Derivation Methods

### Marginfi
- Uses standard Marginfi account structure
- Accounts derived from wallet pubkey and token mint

### Kamino
- `derive_kamino_lending_market`: Seeds: `[b"lending_market", token_mint]`
- `derive_kamino_reserve`: Seeds: `[b"reserve", lending_market, token_mint]`
- `derive_kamino_reserve_liquidity_supply`: Seeds: `[b"liquidity_supply", reserve]`
- `derive_kamino_reserve_collateral_mint`: Seeds: `[b"collateral_mint", reserve]`
- `derive_kamino_lending_market_authority`: Seeds: `[b"lending_market_authority", lending_market]`

### Solend
- `derive_solend_lending_market`: Seeds: `[b"lending_market", token_mint]`
- `derive_solend_reserve`: Seeds: `[b"reserve", lending_market, token_mint]`
- `derive_solend_reserve_liquidity_supply`: Seeds: `[b"liquidity_supply", reserve]`
- `derive_solend_lending_market_authority`: Seeds: `[b"lending_market_authority", lending_market]`

### Mango v4
- `derive_mango_group`: Uses known constant `78b8f4cGCwmZ9ysPFMWLaLTkkaYnUjwMJYStWe5RTSSX`
- `derive_mango_account`: Seeds: `[b"MangoAccount", group, owner, account_num]`
- `derive_mango_token_bank`: Seeds: `[b"Bank", group, token_mint]`
- `derive_mango_token_vault`: Seeds: `[b"Vault", token_bank]`
- `derive_mango_flash_loan_type`: Seeds: `[b"FlashLoanType", group]`

## SPL Compliance

### Flash Loan Receiver Program
- All implementations follow SPL flash loan design
- Receiver program must handle repayment within same transaction
- Instruction tag 0 used for receiver program instructions
- Automatic repayment validation by lending programs

### Account Structure
- All implementations use proper account ordering as specified in SPL design
- Required accounts include source liquidity, destination, reserve, market authority
- Proper signer requirements enforced

## Next Steps

### 🔄 Pending Implementation
1. **Flash Loan Receiver Program**: Create SPL-compliant receiver program to handle flash loan execution and repayment
2. **Account Management**: Implement proper account derivation and validation for production use
3. **Provider Integration**: Connect to real provider APIs for accurate liquidity and fee information
4. **Testing**: Comprehensive unit and integration testing of instruction encoding

### 🔍 Validation Required
- Verify discriminators against actual program IDLs
- Test account derivation with real provider data
- Validate instruction data serialization format
- Confirm SPL compliance with flash loan receiver program

## Security Considerations

- All instructions use proper discriminators to prevent instruction confusion
- Account validation ensures correct program ownership
- Signer requirements properly enforced
- Flash loan receiver program must validate repayment amounts and timing

## References

- [SPL Flash Loan Design](../DEX_INTEGRATION_REFERENCE.md#spl-flash-loan-design)
- [Marginfi TypeScript SDK](https://github.com/mrgnlabs/marginfi-v2/tree/main/packages/marginfi-client-v2)
- [Kamino SPL Token-Lending](https://github.com/Kamino-Finance/klend)
- [Mango v4 Program](https://github.com/blockworks-foundation/mango-v4)
- [Solend Protocol](https://github.com/solendprotocol/solana-program-library)
