//! Comprehensive tests for the Priority Queue System
//! 
//! Tests all aspects of the priority queue including opportunity ranking,
//! gas efficiency scoring, success probability calculation, and execution ordering.

use std::time::{Duration, Instant};
use std::collections::HashMap;
use tokio::time::sleep;
use solana_sdk::pubkey::Pubkey;
use uuid::Uuid;

use solana_vntr_sniper::core::priority_queue::{
    OpportunityQueue, PriorityQueueConfig, ArbitrageOpportunity, ArbitrageStrategy,
    PerformanceMetrics, QueueStats,
};

/// Helper function to create a test arbitrage opportunity
fn create_test_opportunity(
    profit_pct: f64,
    gas_cost: u64,
    liquidity: u64,
    strategy: ArbitrageStrategy,
    route_complexity: u8,
) -> ArbitrageOpportunity {
    let now = Instant::now();
    ArbitrageOpportunity {
        id: Uuid::new_v4().to_string(),
        token_mint: Pubkey::new_unique(),
        strategy_type: strategy,
        buy_dex: "raydium".to_string(),
        sell_dex: "orca".to_string(),
        buy_price: 1.0,
        sell_price: 1.0 + (profit_pct / 100.0),
        price_difference_pct: profit_pct,
        estimated_profit_sol: profit_pct / 100.0,
        estimated_profit_pct: profit_pct,
        liquidity_available: liquidity,
        detected_at: now,
        expires_at: now + Duration::from_secs(30),
        trade_amount: 100_000_000, // 0.1 SOL
        estimated_gas_cost: gas_cost,
        route_complexity,
    }
}

#[tokio::test]
async fn test_priority_queue_basic_operations() {
    let queue = OpportunityQueue::new();
    
    // Test empty queue
    assert_eq!(queue.size().await, 0);
    assert!(queue.get_next_opportunity().await.is_none());
    assert!(queue.peek_next_opportunity().await.is_none());
    
    // Add an opportunity
    let opportunity = create_test_opportunity(
        2.5, // 2.5% profit
        150_000, // gas cost
        1_000_000_000, // 1 SOL liquidity
        ArbitrageStrategy::MultiDex,
        1,
    );
    
    queue.add_opportunity(opportunity.clone()).await.unwrap();
    assert_eq!(queue.size().await, 1);
    
    // Peek should not remove the opportunity
    let peeked = queue.peek_next_opportunity().await.unwrap();
    assert_eq!(peeked.opportunity.id, opportunity.id);
    assert_eq!(queue.size().await, 1);
    
    // Get should remove the opportunity
    let retrieved = queue.get_next_opportunity().await.unwrap();
    assert_eq!(retrieved.opportunity.id, opportunity.id);
    assert_eq!(queue.size().await, 0);
}

#[tokio::test]
async fn test_priority_ordering() {
    let queue = OpportunityQueue::new();
    
    // Create opportunities with different profit levels
    let low_profit = create_test_opportunity(1.0, 150_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1);
    let medium_profit = create_test_opportunity(3.0, 150_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1);
    let high_profit = create_test_opportunity(5.0, 150_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1);
    
    // Add in random order
    queue.add_opportunity(medium_profit.clone()).await.unwrap();
    queue.add_opportunity(low_profit.clone()).await.unwrap();
    queue.add_opportunity(high_profit.clone()).await.unwrap();
    
    // Should retrieve in order of highest profit first
    let first = queue.get_next_opportunity().await.unwrap();
    assert_eq!(first.opportunity.id, high_profit.id);
    
    let second = queue.get_next_opportunity().await.unwrap();
    assert_eq!(second.opportunity.id, medium_profit.id);
    
    let third = queue.get_next_opportunity().await.unwrap();
    assert_eq!(third.opportunity.id, low_profit.id);
}

#[tokio::test]
async fn test_gas_efficiency_scoring() {
    let queue = OpportunityQueue::new();
    
    // Same profit, different gas costs
    let high_gas = create_test_opportunity(2.0, 500_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1);
    let low_gas = create_test_opportunity(2.0, 100_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1);
    
    queue.add_opportunity(high_gas.clone()).await.unwrap();
    queue.add_opportunity(low_gas.clone()).await.unwrap();
    
    // Lower gas cost should have higher priority
    let first = queue.get_next_opportunity().await.unwrap();
    assert_eq!(first.opportunity.id, low_gas.id);
    assert!(first.gas_efficiency_score > 0.0);
}

#[tokio::test]
async fn test_liquidity_scoring() {
    let queue = OpportunityQueue::new();
    
    // Same profit, different liquidity
    let low_liquidity = create_test_opportunity(2.0, 150_000, 100_000_000, ArbitrageStrategy::MultiDex, 1); // 0.1 SOL
    let high_liquidity = create_test_opportunity(2.0, 150_000, 10_000_000_000, ArbitrageStrategy::MultiDex, 1); // 10 SOL
    
    queue.add_opportunity(low_liquidity.clone()).await.unwrap();
    queue.add_opportunity(high_liquidity.clone()).await.unwrap();
    
    // Higher liquidity should have higher priority
    let first = queue.get_next_opportunity().await.unwrap();
    assert_eq!(first.opportunity.id, high_liquidity.id);
    assert!(first.liquidity_score > 0.0);
}

#[tokio::test]
async fn test_strategy_complexity_scoring() {
    let queue = OpportunityQueue::new();
    
    // Same profit, different complexity
    let simple = create_test_opportunity(2.0, 150_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1);
    let complex = create_test_opportunity(
        2.0, 
        300_000, 
        1_000_000_000, 
        ArbitrageStrategy::TwoHop { intermediate_token: Pubkey::new_unique() }, 
        2
    );
    
    queue.add_opportunity(complex.clone()).await.unwrap();
    queue.add_opportunity(simple.clone()).await.unwrap();
    
    // Simpler strategy should have higher priority due to higher success probability
    let first = queue.get_next_opportunity().await.unwrap();
    assert_eq!(first.opportunity.id, simple.id);
}

#[tokio::test]
async fn test_time_decay_scoring() {
    let queue = OpportunityQueue::new();
    
    // Create an old opportunity
    let now = Instant::now();
    let old_opportunity = ArbitrageOpportunity {
        id: Uuid::new_v4().to_string(),
        token_mint: Pubkey::new_unique(),
        strategy_type: ArbitrageStrategy::MultiDex,
        buy_dex: "raydium".to_string(),
        sell_dex: "orca".to_string(),
        buy_price: 1.0,
        sell_price: 1.02,
        price_difference_pct: 2.0,
        estimated_profit_sol: 0.02,
        estimated_profit_pct: 2.0,
        liquidity_available: 1_000_000_000,
        detected_at: now - Duration::from_secs(20), // 20 seconds old
        expires_at: now + Duration::from_secs(10),
        trade_amount: 100_000_000,
        estimated_gas_cost: 150_000,
        route_complexity: 1,
    };
    
    let new_opportunity = create_test_opportunity(2.0, 150_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1);
    
    queue.add_opportunity(old_opportunity.clone()).await.unwrap();
    queue.add_opportunity(new_opportunity.clone()).await.unwrap();
    
    // Newer opportunity should have higher priority due to time decay
    let first = queue.get_next_opportunity().await.unwrap();
    assert_eq!(first.opportunity.id, new_opportunity.id);
    assert!(first.time_decay_score > 0.8); // Should be close to 1.0 for new opportunity
}

#[tokio::test]
async fn test_opportunity_expiration() {
    let queue = OpportunityQueue::new();
    
    // Create an expired opportunity
    let now = Instant::now();
    let expired_opportunity = ArbitrageOpportunity {
        id: Uuid::new_v4().to_string(),
        token_mint: Pubkey::new_unique(),
        strategy_type: ArbitrageStrategy::MultiDex,
        buy_dex: "raydium".to_string(),
        sell_dex: "orca".to_string(),
        buy_price: 1.0,
        sell_price: 1.02,
        price_difference_pct: 2.0,
        estimated_profit_sol: 0.02,
        estimated_profit_pct: 2.0,
        liquidity_available: 1_000_000_000,
        detected_at: now - Duration::from_secs(60),
        expires_at: now - Duration::from_secs(1), // Already expired
        trade_amount: 100_000_000,
        estimated_gas_cost: 150_000,
        route_complexity: 1,
    };
    
    // Should not add expired opportunity
    queue.add_opportunity(expired_opportunity).await.unwrap();
    assert_eq!(queue.size().await, 0);
}

#[tokio::test]
async fn test_performance_metrics_update() {
    let queue = OpportunityQueue::new();
    
    let opportunity = create_test_opportunity(2.0, 150_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1);
    
    // Update performance metrics
    queue.update_performance_metrics(
        &opportunity,
        true, // success
        0.02, // actual profit
        140_000, // actual gas cost
        Duration::from_millis(500), // execution time
    ).await.unwrap();
    
    let metrics = queue.get_performance_metrics().await;
    assert_eq!(metrics.total_trades, 1);
    assert_eq!(metrics.successful_trades, 1);
    assert_eq!(metrics.total_profit_sol, 0.02);
    
    // Check DEX success rate
    let dex_key = "raydium_orca";
    assert!(metrics.dex_success_rates.contains_key(dex_key));
    assert!(metrics.dex_success_rates[dex_key] > 0.0);
}

#[tokio::test]
async fn test_queue_size_limit() {
    let config = PriorityQueueConfig {
        max_queue_size: 3,
        ..Default::default()
    };
    let queue = OpportunityQueue::with_config(config);
    
    // Add more opportunities than the limit
    for i in 0..5 {
        let opportunity = create_test_opportunity(
            (i + 1) as f64, // Different profits
            150_000,
            1_000_000_000,
            ArbitrageStrategy::MultiDex,
            1,
        );
        queue.add_opportunity(opportunity).await.unwrap();
    }
    
    // Should only keep the top 3
    assert_eq!(queue.size().await, 3);
    
    // Should have the highest profit opportunities
    let first = queue.get_next_opportunity().await.unwrap();
    assert!(first.opportunity.estimated_profit_pct >= 3.0);
}

#[tokio::test]
async fn test_queue_statistics() {
    let queue = OpportunityQueue::new();
    
    // Add various opportunities
    let multi_dex = create_test_opportunity(2.0, 150_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1);
    let two_hop = create_test_opportunity(
        3.0, 
        300_000, 
        1_000_000_000, 
        ArbitrageStrategy::TwoHop { intermediate_token: Pubkey::new_unique() }, 
        2
    );
    
    queue.add_opportunity(multi_dex).await.unwrap();
    queue.add_opportunity(two_hop).await.unwrap();
    
    let stats = queue.get_queue_stats().await;
    assert_eq!(stats.total_opportunities, 2);
    assert!(stats.average_composite_score > 0.0);
    assert_eq!(stats.strategy_distribution.len(), 2);
    assert!(stats.oldest_opportunity_age.is_some());
}

#[tokio::test]
async fn test_composite_scoring_weights() {
    let config = PriorityQueueConfig {
        profit_weight: 0.5,
        gas_efficiency_weight: 0.3,
        success_probability_weight: 0.1,
        liquidity_weight: 0.05,
        time_decay_weight: 0.05,
        ..Default::default()
    };
    let queue = OpportunityQueue::with_config(config);
    
    // High profit, high gas cost
    let high_profit_high_gas = create_test_opportunity(5.0, 500_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1);
    // Low profit, low gas cost
    let low_profit_low_gas = create_test_opportunity(1.5, 50_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1);
    
    queue.add_opportunity(low_profit_low_gas.clone()).await.unwrap();
    queue.add_opportunity(high_profit_high_gas.clone()).await.unwrap();
    
    // With high profit weight, high profit should win despite high gas cost
    let first = queue.get_next_opportunity().await.unwrap();
    assert_eq!(first.opportunity.id, high_profit_high_gas.id);
    assert!(first.composite_score > 0.0);
}

#[tokio::test]
async fn test_minimum_thresholds() {
    let config = PriorityQueueConfig {
        min_profit_threshold_bps: 200, // 2%
        max_gas_cost_sol: 0.005, // 0.005 SOL max gas
        min_liquidity_threshold: 500_000_000, // 0.5 SOL min liquidity
        ..Default::default()
    };
    let queue = OpportunityQueue::with_config(config);
    
    // Below profit threshold
    let low_profit = create_test_opportunity(1.0, 150_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1);
    queue.add_opportunity(low_profit).await.unwrap();
    assert_eq!(queue.size().await, 0);
    
    // Above gas cost threshold
    let high_gas = create_test_opportunity(3.0, 10_000_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1); // 0.01 SOL gas
    queue.add_opportunity(high_gas).await.unwrap();
    assert_eq!(queue.size().await, 0);
    
    // Below liquidity threshold
    let low_liquidity = create_test_opportunity(3.0, 150_000, 100_000_000, ArbitrageStrategy::MultiDex, 1); // 0.1 SOL liquidity
    queue.add_opportunity(low_liquidity).await.unwrap();
    assert_eq!(queue.size().await, 0);
    
    // Valid opportunity
    let valid = create_test_opportunity(3.0, 150_000, 1_000_000_000, ArbitrageStrategy::MultiDex, 1);
    queue.add_opportunity(valid).await.unwrap();
    assert_eq!(queue.size().await, 1);
}
