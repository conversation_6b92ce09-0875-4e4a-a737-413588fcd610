use std::time::{Duration, Instant};
use tokio::time::sleep;
use solana_vntr_sniper::{
    core::{
        app_state::EnhancedAppState,
        circuit_breaker::{CircuitBreaker, CircuitBreakerConfig},
        lock_free_globals::{LockFreeGlobals, TokenTrackingInfo},
        rate_limiter::{RateLimiter, RateLimiterConfig},
        retry::{RetryConfig, retry_with_backoff},
    },
};
use colored::Colorize;
use anchor_client::solana_sdk::signature::Keypair;

/// Performance validation test suite
async fn test_semaphore_rate_limiting() {
    println!("{}", "=== Testing Semaphore Rate Limiting ===".blue().bold());

    // Create rate limiter with limited permits
    let config = RateLimiterConfig {
        max_concurrent_rpc_calls: 3,
        max_concurrent_swaps: 2,
        max_concurrent_price_checks: 5,
        max_concurrent_pool_discovery: 2,
    };
    let rate_limiter = RateLimiter::new(config);

    // Test RPC semaphore (3 permits)
    let start = Instant::now();
    let mut handles = vec![];

    for i in 0..6 {
        let limiter = rate_limiter.clone();
        let handle = tokio::spawn(async move {
            let result = limiter.execute_rpc(|| async {
                println!("RPC call {} acquired permit", i);
                sleep(Duration::from_millis(100)).await;
                println!("RPC call {} released permit", i);
                Ok::<(), anyhow::Error>(())
            }).await;
            result.unwrap();
        });
        handles.push(handle);
    }

    // Wait for all tasks
    for handle in handles {
        handle.await.unwrap();
    }

    let duration = start.elapsed();
    println!("RPC semaphore test completed in: {:?}", duration);

    // Should take longer than 100ms due to rate limiting (6 tasks, 3 permits, 100ms each)
    assert!(duration > Duration::from_millis(150));
    println!("{}", "✅ RPC Semaphore rate limiting working correctly".green());
}

async fn test_swap_semaphore_limiting() {
    println!("{}", "=== Testing Swap Semaphore Limiting ===".blue().bold());

    // Create rate limiter with limited swap permits
    let config = RateLimiterConfig {
        max_concurrent_rpc_calls: 10,
        max_concurrent_swaps: 2,
        max_concurrent_price_checks: 10,
        max_concurrent_pool_discovery: 10,
    };
    let rate_limiter = RateLimiter::new(config);

    let start = Instant::now();
    let mut handles = vec![];

    // Test with 4 concurrent swaps (limit is 2)
    for i in 0..4 {
        let limiter = rate_limiter.clone();
        let handle = tokio::spawn(async move {
            let result = limiter.execute_swap(|| async {
                println!("Swap {} acquired permit", i);
                sleep(Duration::from_millis(200)).await;
                println!("Swap {} released permit", i);
                Ok::<(), anyhow::Error>(())
            }).await;
            result.unwrap();
        });
        handles.push(handle);
    }

    for handle in handles {
        handle.await.unwrap();
    }

    let duration = start.elapsed();
    println!("Swap semaphore test completed in: {:?}", duration);

    // Should take longer due to only 2 concurrent swaps allowed (4 tasks, 2 permits, 200ms each)
    assert!(duration > Duration::from_millis(350));
    println!("{}", "✅ Swap Semaphore rate limiting working correctly".green());
}

async fn test_circuit_breaker_functionality() {
    println!("{}", "=== Testing Circuit Breaker Functionality ===".blue().bold());

    let config = CircuitBreakerConfig {
        failure_threshold: 3,
        recovery_timeout: Duration::from_millis(500),
        half_open_max_calls: 2,
        half_open_success_threshold: 1,
    };

    let circuit_breaker = CircuitBreaker::new("test_breaker".to_string(), config);

    // Test failure accumulation
    for i in 0..3 {
        let result = circuit_breaker.execute(|| async {
            Err::<(), anyhow::Error>(anyhow::anyhow!("Simulated failure"))
        }).await;

        assert!(result.is_err());
        println!("Failure {} recorded", i + 1);
    }

    // Circuit should now be open
    let result = circuit_breaker.execute(|| async {
        Ok::<(), anyhow::Error>(())
    }).await;

    assert!(result.is_err());
    println!("{}", "✅ Circuit breaker opened after threshold failures".green());

    // Wait for timeout
    sleep(Duration::from_millis(600)).await;

    // Should be in half-open state, test recovery
    let result = circuit_breaker.execute(|| async {
        Ok::<(), anyhow::Error>(())
    }).await;

    assert!(result.is_ok());
    println!("{}", "✅ Circuit breaker recovered to half-open state".green());
}

async fn test_lock_free_global_state() {
    println!("{}", "=== Testing Lock-Free Global State ===".blue().bold());

    use std::sync::Arc;
    let globals = Arc::new(LockFreeGlobals::new());
    let start = Instant::now();
    let mut handles = vec![];

    // Test concurrent access to global token tracking
    for i in 0..100 {
        let globals_clone = Arc::clone(&globals);
        let handle = tokio::spawn(async move {
            let mint = format!("test_mint_{}", i);
            let mut info = TokenTrackingInfo::new();
            info.update_price(100.0 + i as f64);

            // Insert into global token tracking
            globals_clone.update_token_tracking(mint.clone(), info.clone());

            // Read back
            if let Some(retrieved) = globals_clone.get_token_tracking(&mint) {
                assert_eq!(retrieved.price_history.len(), info.price_history.len());
            }

            // Test buying enabled flag
            assert!(globals_clone.is_buying_enabled());
        });
        handles.push(handle);
    }

    for handle in handles {
        handle.await.unwrap();
    }

    let duration = start.elapsed();
    println!("Lock-free operations completed in: {:?}", duration);

    // Verify final state
    let all_tokens = globals.get_all_tracked_tokens();
    assert_eq!(all_tokens.len(), 100);

    println!("{}", "✅ Lock-free global state working correctly".green());
}

async fn test_retry_logic_with_exponential_backoff() {
    println!("{}", "=== Testing Retry Logic with Exponential Backoff ===".blue().bold());

    let config = RetryConfig {
        max_attempts: 3,
        base_delay: Duration::from_millis(100),
        max_delay: Duration::from_millis(1000),
        backoff_multiplier: 2.0,
        jitter: true,
    };

    let start = Instant::now();

    // Use Arc<AtomicU32> for thread-safe counter
    use std::sync::{Arc, atomic::{AtomicU32, Ordering}};
    let attempt_count = Arc::new(AtomicU32::new(0));
    let attempt_count_clone = Arc::clone(&attempt_count);

    let result = retry_with_backoff(&config, move || {
        let count = attempt_count_clone.fetch_add(1, Ordering::SeqCst) + 1;
        println!("Attempt {}", count);

        async move {
            if count < 3 {
                Err(anyhow::anyhow!("Simulated failure"))
            } else {
                Ok("Success".to_string())
            }
        }
    }).await;

    let duration = start.elapsed();
    let final_count = attempt_count.load(Ordering::SeqCst);

    assert!(result.is_ok());
    assert_eq!(final_count, 3);

    // Should have taken time for backoff delays
    assert!(duration > Duration::from_millis(200));

    println!("Retry logic completed in: {:?}", duration);
    println!("{}", "✅ Retry logic with exponential backoff working correctly".green());
}

async fn test_connection_pool_health() {
    println!("{}", "=== Testing Connection Pool Health ===".blue().bold());

    // Load environment variables
    dotenv::dotenv().ok();

    // Create enhanced app state with connection pool
    let keypair = Keypair::new();
    let rpc_endpoint = std::env::var("RPC_HTTP")
        .unwrap_or_else(|_| "https://api.devnet.solana.com".to_string());

    let app_state = match EnhancedAppState::from_env(keypair, rpc_endpoint).await {
        Ok(state) => state,
        Err(e) => {
            println!("⚠️  Could not create EnhancedAppState: {}. Skipping connection pool test.", e);
            return;
        }
    };

    let start = Instant::now();
    let mut handles = vec![];

    // Test concurrent connection usage
    for i in 0..10 {
        let pool = app_state.rpc_pool.clone();
        let handle = tokio::spawn(async move {
            match pool.get().await {
                Ok(conn) => {
                    println!("Connection {} acquired successfully", i);
                    // Simulate some work
                    sleep(Duration::from_millis(50)).await;
                    drop(conn);
                    println!("Connection {} released", i);
                    true
                },
                Err(e) => {
                    println!("Connection {} failed: {}", i, e);
                    false
                }
            }
        });
        handles.push(handle);
    }

    let mut success_count = 0;
    for handle in handles {
        if handle.await.unwrap() {
            success_count += 1;
        }
    }

    let duration = start.elapsed();
    println!("Connection pool test completed in: {:?}", duration);
    println!("Successful connections: {}/10", success_count);

    // At least some connections should succeed
    assert!(success_count > 0);
    println!("{}", "✅ Connection pool health check passed".green());
}

async fn test_api_credentials_integration() {
    println!("{}", "=== Testing API Credentials Integration ===".blue().bold());
    
    // Load environment variables
    dotenv::dotenv().ok();
    
    // Test Helius RPC endpoint
    let rpc_url = std::env::var("RPC_HTTP").unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string());
    println!("Testing RPC endpoint: {}", rpc_url);
    
    // Test Birdeye API key
    let birdeye_key = std::env::var("BIRDEYE_API_KEY").unwrap_or_else(|_| "test_key".to_string());
    println!("Birdeye API key configured: {}", if birdeye_key != "test_key" { "✅" } else { "⚠️" });
    
    // Basic connectivity test
    let client = reqwest::Client::new();
    let response = client
        .get(&format!("{}/health", rpc_url))
        .timeout(Duration::from_secs(5))
        .send()
        .await;
    
    match response {
        Ok(_) => println!("{}", "✅ RPC endpoint connectivity verified".green()),
        Err(e) => println!("⚠️  RPC endpoint test failed: {}", e),
    }
    
    println!("{}", "✅ API credentials integration test completed".green());
}

/// Run all performance validation tests
#[tokio::test]
async fn run_comprehensive_performance_validation() {
    println!("{}", "🚀 COMPREHENSIVE PERFORMANCE VALIDATION SUITE 🚀".cyan().bold());
    println!("{}", "=" .repeat(60).cyan());
    
    // Run individual test components
    test_semaphore_rate_limiting().await;
    test_swap_semaphore_limiting().await;
    test_circuit_breaker_functionality().await;
    test_lock_free_global_state().await;
    test_retry_logic_with_exponential_backoff().await;
    test_connection_pool_health().await;
    test_api_credentials_integration().await;
    
    println!("{}", "=" .repeat(60).cyan());
    println!("{}", "🎉 ALL PERFORMANCE VALIDATION TESTS COMPLETED SUCCESSFULLY! 🎉".green().bold());
}
