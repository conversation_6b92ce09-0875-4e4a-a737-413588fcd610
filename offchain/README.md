
# 🚀 Solana Arbitrage Bot - Live Trading System

**PRODUCTION READY** - Real market arbitrage trading system for Solana DEXes

This is the live trading component of the Solana arbitrage bot that monitors multiple DEXes for arbitrage opportunities and executes trades using flash loans with real market data and transactions.

## ⚠️ IMPORTANT SECURITY NOTICE

**This bot trades with real money on mainnet. Please ensure you:**
- Use a dedicated trading wallet with limited funds
- Test thoroughly in dry-run mode first
- Monitor your trades closely
- Have emergency stop procedures in place

## 🎯 Features

- **Multi-DEX Arbitrage**: Monitors Raydium, Orca, Meteora, and PumpSwap simultaneously
- **Flash Loan Integration**: Executes capital-efficient arbitrage without requiring large upfront capital
- **Real-time Market Data**: Uses Helius RPC and Birdeye API for live price feeds
- **Risk Management**: Built-in circuit breakers, rate limiting, and emergency stops
- **Performance Optimized**: Semaphore-based concurrency control and lock-free data structures
- **MEV Protection**: Optional Jito bundle integration for transaction protection

## 🛠 Prerequisites

1. **Rust** (latest stable version)
2. **Solana CLI** tools
3. **Trading wallet** with SOL for gas fees and initial capital
4. **API Keys**:
   - Helius RPC endpoint
   - Birdeye API key

## 📋 Setup Instructions

### 1. Clone and Build

```bash
git clone https://github.com/deniyuda348/Solana-Arbitrage-Bot-Flash-Loan.git
cd Solana-Arbitrage-Bot-Flash-Loan/offchain
cargo build --release
```

### 2. Configure Environment

Copy the `.env` file and update with your credentials:

```bash
cp .env .env.backup
```

**CRITICAL**: Update the following in `.env`:

```env
# Replace with your actual trading wallet private key
PRIVATE_KEY=YOUR_ACTUAL_PRIVATE_KEY_HERE

# Your API endpoints (already configured with provided keys)
RPC_HTTP=https://mainnet.helius-rpc.com/?api-key=492bf341-a995-4f0c-91bb-16cbfa13e3ba
BIRDEYE_API_KEY=eaa7c23226204f3091f042f0aa13b690

# Trading parameters (adjust as needed)
DEFAULT_TRADE_AMOUNT=0.05
MAX_SLIPPAGE_BPS=100
MIN_PROFIT_THRESHOLD_BPS=75
```

### 3. Validate Configuration

Test your setup before live trading:

```bash
# Test API connectivity and wallet setup
cargo run --release -- live-trade --dry-run

# Test with custom trade amount
cargo run --release -- live-trade --dry-run --max-trade-amount 0.1
```

## 🚀 Live Trading Commands

### Start Live Trading

```bash
# Start live trading with default settings
cargo run --release -- live-trade

# Start with custom parameters
cargo run --release -- live-trade --max-trade-amount 0.1

# Force start (skip warnings)
cargo run --release -- live-trade --force
```

### Monitor Mode (No Execution)

```bash
# Monitor arbitrage opportunities without executing
cargo run --release -- monitor --threshold 1.5 --min-liquidity 10.0
```

### Pool Discovery

```bash
# Discover and cache pool data
cargo run --release -- pools --save
```

## 📊 Trading Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `DEFAULT_TRADE_AMOUNT` | 0.05 SOL | Amount per arbitrage trade |
| `MAX_SLIPPAGE_BPS` | 100 (1%) | Maximum acceptable slippage |
| `MIN_PROFIT_THRESHOLD_BPS` | 75 (0.75%) | Minimum profit to execute trade |
| `MAX_CONCURRENT_SWAPS` | 3 | Maximum simultaneous trades |
| `MAX_CONCURRENT_RPC_CALLS` | 15 | RPC rate limiting |

## 🛡 Safety Features

### Emergency Controls

```env
# Emergency stop (stops all trading immediately)
EMERGENCY_STOP=false

# Enable emergency liquidation of positions
ENABLE_EMERGENCY_LIQUIDATION=true
```

### Circuit Breaker

```env
# Circuit breaker settings
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60
```

### Rate Limiting

```env
# Semaphore-based rate limiting
MAX_CONCURRENT_RPC_CALLS=15
MAX_CONCURRENT_SWAPS=3
MAX_CONCURRENT_PRICE_CHECKS=25
```

## 📈 Monitoring and Logs

The bot provides real-time logging of:
- Arbitrage opportunities detected
- Trade executions and results
- Performance metrics
- Error conditions and circuit breaker status

Example output:
```
🚀 LIVE TRADING ACTIVE - Real market execution enabled
✅ Wallet: 7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU
✅ Balance: 1.2500 SOL
✅ Default trade amount: 0.0500 SOL
📊 Monitoring 4 DEXes for arbitrage opportunities
💹 Live trading active - Iteration 30
📈 Performance: 15 opportunities detected, 3 executed
```

## ⚡ Performance Optimization

The bot is optimized for high-frequency trading with:

- **Semaphore-based concurrency**: Prevents RPC overload
- **Circuit breakers**: Automatic failure recovery
- **Lock-free data structures**: Minimal contention
- **Efficient memory management**: Optimized for low latency

## 🔧 Troubleshooting

### Common Issues

1. **"PRIVATE_KEY not set"**
   - Update `.env` with your actual wallet private key

2. **"RPC connection failed"**
   - Check your internet connection
   - Verify RPC endpoint is accessible

3. **"Insufficient balance"**
   - Ensure wallet has enough SOL for gas fees
   - Check `DEFAULT_TRADE_AMOUNT` is not too high

4. **"No arbitrage opportunities"**
   - Market conditions may not be favorable
   - Try lowering `MIN_PROFIT_THRESHOLD_BPS`

### Emergency Procedures

If you need to stop trading immediately:

1. **Set emergency stop**: `EMERGENCY_STOP=true` in `.env`
2. **Kill the process**: `Ctrl+C` or `kill` command
3. **Check positions**: Verify all positions are closed

## 📞 Support

For issues or questions:
- Check the logs for error messages
- Verify your `.env` configuration
- Ensure sufficient wallet balance
- Monitor network conditions

## ⚖️ Disclaimer

**This software is for educational and research purposes. Trading cryptocurrencies involves substantial risk of loss. Use at your own risk. The developers are not responsible for any financial losses.**

---

**Happy Trading! 🎯**