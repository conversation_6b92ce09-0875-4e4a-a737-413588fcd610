# DEX Integration Reference Guide

This document provides a comprehensive reference for integrating with major Solana DEXes in our arbitrage bot.

## Raydium Integration

### Resources
- **Raydium SDK V2**: https://crates.io/crates/raydium-sdk-V2
- **Rust Swap Implementation**: https://solana.stackexchange.com/questions/14259/raydium-swap-with-rust
- **Official GitHub**: https://github.com/raydium-io

### Key Implementation Details
- **Program IDs**: 
  - AMM: `675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8`
  - CLMM: `CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK`
  - CPMM: `CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C`

- **Pool Structure**: Raydium pools contain token reserves, fees, and swap calculations
- **Swap Calculation**: Uses constant product formula with fees
- **Account Fetching**: Use `get_program_accounts` with proper filters for pool discovery

### Code Patterns
```rust
// Pool fetching with filters
let pools = get_program_accounts_with_filter(
    &rpc_client,
    &program_id,
    pool_size,
    &token_mint_position,
    &mint_pubkey
);

// Swap calculation
fn calculate_swap_output(&self, input_amount: u64, input_is_token_a: bool) -> Result<u64> {
    // Implement constant product formula with fees
}
```

## Orca Whirlpools Integration

### Resources
- **Official GitHub**: https://github.com/orca-so/whirlpools
- **Documentation**: https://dev.orca.so/orca_whirlpools_docs/
- **Rust Client**: https://crates.io/crates/orca_whirlpools_client

### Key Implementation Details
- **Program ID**: `whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc`
- **Concentrated Liquidity**: Uses tick-based pricing model
- **Price Calculation**: Based on sqrt price and tick spacing
- **Fee Structure**: Multiple fee tiers (0.01%, 0.05%, 0.3%, 1%)

### Code Patterns
```rust
use orca_whirlpools_client::*;

// Whirlpool client initialization
let whirlpool_client = WhirlpoolClient::new(rpc_client);

// Price calculation for concentrated liquidity
fn get_price_from_sqrt_price(sqrt_price: u128) -> f64 {
    let price = (sqrt_price as f64 / (2_u128.pow(64) as f64)).powi(2);
    price
}
```

## Meteora DLMM Integration

### Resources
- **DLMM SDK**: https://github.com/MeteoraAg/dlmm-sdk
- **Documentation**: https://docs.meteora.ag/integration/dynamic-vault-integration/using-rust-client

### Key Implementation Details
- **Program ID**: `LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo`
- **Dynamic Liquidity**: Bin-based liquidity distribution
- **Fee Structure**: Dynamic fees based on volatility
- **Bin Management**: Active bins for current price range

### Code Patterns
```rust
// DLMM pool interaction
pub struct DLMMPool {
    pub bin_step: u16,
    pub active_bin_id: i32,
    pub reserve_x: u64,
    pub reserve_y: u64,
}

// Swap calculation for DLMM
fn calculate_dlmm_swap(&self, amount_in: u64, swap_for_y: bool) -> Result<u64> {
    // Implement bin-based swap calculation
}
```

## Flash Loan Integration (Solana Program Library)

### Official Flash Loan Design Reference

Based on the official Solana Program Library flash loan design document, flash loans follow a specific pattern that our implementation must adhere to for compatibility and security.

#### Flash Loan Instruction Structure

```rust
pub enum LendingInstruction {
    /// Make a flash loan.
    ///
    /// Accounts expected by this instruction:
    ///
    /// 0. `[writable]` Source liquidity token account.
    ///    Minted by reserve liquidity mint.
    ///    Must match the reserve liquidity supply.
    /// 1. `[writable]` Destination liquidity token account.
    ///    Minted by reserve liquidity mint.
    /// 2. `[writable]` Reserve account.
    /// 3. `[]` Lending market account.
    /// 4. `[]` Derived lending market authority.
    /// 5. `[]` Flash loan receiver program account.
    ///    Must implement an instruction that has tag of 0 and a signature of `(repay_amount: u64)`
    ///    This instruction must return the amount to the source liquidity account.
    /// 6. `[]` Token program id.
    /// 7. `[writable]` Flash loan fee receiver account.
    ///    Must match the reserve liquidity fee receiver.
    /// 8. `[writable]` Host fee receiver.
    /// .. `[any]` Additional accounts expected by the receiving program's `ReceiveFlashLoan` instruction.
    FlashLoan {
        /// The amount that is to be borrowed
        amount: u64,
    },
}
```

#### Flash Loan Execution Flow

The official implementation follows this order:

1. **Safety Checks and Fee Calculation**: Validate loan parameters and calculate fees
2. **Transfer Funds**: Transfer `amount` from source liquidity account to destination liquidity account
3. **Call ReceiveFlashLoan**: Invoke the flash loan receiver program with tag `0`
4. **Verify Repayment**: Check that the returned amount with fees is back in the reserve account

#### Flash Loan Receiver Program

```rust
pub enum FlashLoanReceiverInstruction {
    /// Receive a flash loan and perform user-defined operation and finally return the fund back.
    ///
    /// Accounts expected:
    ///
    /// 0. `[writable]` Source liquidity (matching the destination from above).
    /// 1. `[writable]` Destination liquidity (matching the source from above).
    /// 2. `[]` Token program id
    /// .. `[any]` Additional accounts provided to the lending program's `FlashLoan` instruction above.
    ReceiveFlashLoan {
        // Amount that is loaned to the receiver program
        amount: u64
    }
}
```

### Critical Implementation Requirements

#### 1. Atomic Transaction Structure
- Flash loan borrow, arbitrage operations, and repayment must be in a single transaction
- No intermediate state where funds are borrowed but not repaid
- All operations must succeed or the entire transaction fails

#### 2. Fee Calculation and Handling
- Flash loan fees must be calculated correctly based on provider rates
- Total repayment = borrowed_amount + fees
- Fee recipients must be properly specified in accounts

#### 3. Account Management
- Source and destination accounts must be properly managed
- Token program accounts must be included
- Authority accounts must have proper permissions

#### 4. Error Handling
- Transaction failure must not leave borrowed funds unreturned
- Proper error propagation from arbitrage operations
- Circuit breaker integration for provider failures

### Sanity Check Results

#### ✅ Correct Patterns in Our Implementation:
1. **Multi-provider Support**: Our `FlashLoanManager` supports multiple providers (Solend, Mango, Marginfi, Kamino)
2. **Atomic Execution**: Flash loan instructions are built as a single transaction
3. **Fee Calculation**: Proper fee calculation based on provider rates
4. **Error Handling**: Circuit breaker and retry logic implemented
5. **Provider Selection**: Best provider selection based on fees and liquidity

#### ⚠️ Areas Requiring Attention:

1. **Instruction Building**: Current implementation uses placeholder instruction data
   ```rust
   // Current placeholder - needs actual instruction encoding
   Ok(Instruction::new_with_bytes(
       FlashLoanProvider::Solend.program_id()?,
       &[0u8; 8], // ❌ Placeholder instruction data
       vec![], // ❌ Placeholder accounts
   ))
   ```

2. **Account Management**: Need to implement proper account derivation for each provider
3. **Receiver Program**: Need to implement our own flash loan receiver program that follows the SPL pattern
4. **Real Provider Integration**: Current implementation uses mock data for provider information

#### 🔧 Required Improvements:

1. **Implement Proper Instruction Encoding**:
   ```rust
   fn create_solend_flash_borrow_instruction(
       &self,
       token_mint: &Pubkey,
       amount: u64,
   ) -> Result<Instruction> {
       // Need to implement actual Solend instruction encoding
       // Following the SPL flash loan pattern
   }
   ```

2. **Create Flash Loan Receiver Program**:
   ```rust
   // Our arbitrage receiver program must implement:
   // - ReceiveFlashLoan instruction with tag 0
   // - Execute arbitrage operations
   // - Return funds + fees to source account
   ```

3. **Real Provider Data Integration**:
   ```rust
   async fn fetch_provider_info(&self, provider: &FlashLoanProvider) -> Result<ProviderLoanInfo> {
       // Replace mock data with real provider API calls
       // Fetch actual liquidity, fees, and availability
   }
   ```

## Common Integration Patterns

### Pool Discovery
```rust
// Generic pool fetching function
pub async fn get_program_accounts_with_filter_async(
    rpc_client: &RpcClient,
    program_id: &Pubkey,
    filters: Vec<RpcFilterType>,
) -> Result<Vec<(Pubkey, Account)>> {
    let config = RpcProgramAccountsConfig {
        filters: Some(filters),
        account_config: RpcAccountInfoConfig {
            encoding: Some(UiAccountEncoding::Base64),
            ..Default::default()
        },
        sort_results: Some(true),
    };
    
    rpc_client.get_program_accounts_with_config(program_id, config).await
}
```

### Swap Output Calculation
```rust
pub trait Pool {
    fn get_token_a_mint(&self) -> Pubkey;
    fn get_token_b_mint(&self) -> Pubkey;
    fn get_token_price(&self) -> Result<f64>;
    fn calculate_swap_output(&self, input_amount: u64, input_is_token_a: bool) -> Result<u64>;
    fn get_estimated_output_amount(&self, input_amount: u64, is_a_to_b: bool) -> Result<u64>;
}
```

### Error Handling
```rust
#[derive(Debug, thiserror::Error)]
pub enum SwapError {
    #[error("Calculation overflow")]
    CalculationOverflow,
    #[error("Invalid input: {0}")]
    InvalidInput(String),
    #[error("Insufficient liquidity")]
    InsufficientLiquidity,
    #[error("Slippage exceeded")]
    SlippageExceeded,
    #[error("Pool not found")]
    PoolNotFound,
    #[error("Invalid pool state")]
    InvalidPoolState,
    #[error("Math error: {0}")]
    MathError(String),
}
```

## Jupiter Aggregator Integration

### Overview
Jupiter is Solana's premier liquidity aggregator that provides optimal swap routes across multiple DEXs. For flash loan arbitrage, Jupiter is essential for:
- **Route Discovery**: Finding optimal paths across multiple DEXs
- **Price Comparison**: Getting best execution prices
- **Liquidity Aggregation**: Accessing deep liquidity pools
- **Atomic Swaps**: Executing complex multi-hop trades

### Jupiter API Integration

#### Quote API (Price Discovery)
```rust
// Get best quote for a swap
let quote_request = QuoteRequest {
    input_mint: "So11111111111111111111111111111111111111112", // SOL
    output_mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", // USDC
    amount: 1_000_000, // 1 SOL (in lamports)
    slippage_bps: 50, // 0.5%
    ..Default::default()
};

let quote = jupiter_client.quote(&quote_request).await?;
```

#### Swap API (Transaction Building)
```rust
// Build swap transaction from quote
let swap_request = SwapRequest {
    user_public_key: wallet.pubkey(),
    quote_response: quote,
    config: TransactionConfig {
        wrap_and_unwrap_sol: true,
        fee_account: Some(fee_account),
        compute_unit_price_micro_lamports: Some(1000),
        ..Default::default()
    },
};

let swap_response = jupiter_client.swap(&swap_request).await?;
let transaction = Transaction::from(swap_response.swap_transaction);
```

### Jupiter Rust Client Integration

#### Dependencies
```toml
[dependencies]
# Jupiter Rust Client
jupiter-swap-api-client = { git = "https://github.com/jup-ag/jupiter-swap-api-client.git" }

# For Node.js/TypeScript integration (if needed)
# @jup-ag/api = "6.0.0"
# @jup-ag/core = "6.0.0"

# Raydium
raydium-sdk-V2 = "0.1.0"

# Orca
orca_whirlpools_client = "0.1.0"

# Meteora (typically integrated via direct program calls)
# No specific crate, use anchor-client for program interaction

# Common Solana dependencies
anchor-client = "0.31.0"
solana-client = "2.1.14"
spl-token = "4.0.0"
arrayref = "0.3.8"
num_enum = "0.7.3"
```

#### Flash Loan Arbitrage with Jupiter
```rust
use jupiter_swap_api_client::{
    quote::QuoteRequest,
    swap::SwapRequest,
    transaction_config::TransactionConfig,
    JupiterSwapApiClient,
};

pub struct JupiterArbitrageEngine {
    client: JupiterSwapApiClient,
    wallet: Keypair,
}

impl JupiterArbitrageEngine {
    pub fn new(api_url: &str, wallet: Keypair) -> Self {
        Self {
            client: JupiterSwapApiClient::new(api_url),
            wallet,
        }
    }

    /// Find arbitrage opportunity using Jupiter routes
    pub async fn find_arbitrage_opportunity(
        &self,
        token_a: Pubkey,
        token_b: Pubkey,
        amount: u64,
    ) -> Result<ArbitrageOpportunity> {
        // Get quote for A -> B
        let quote_a_to_b = self.get_quote(token_a, token_b, amount).await?;

        // Get quote for B -> A using output amount from first quote
        let quote_b_to_a = self.get_quote(
            token_b,
            token_a,
            quote_a_to_b.out_amount
        ).await?;

        // Calculate profit
        let profit = quote_b_to_a.out_amount.saturating_sub(amount);
        let profit_percentage = (profit as f64 / amount as f64) * 100.0;

        if profit_percentage > MIN_PROFIT_THRESHOLD {
            Ok(ArbitrageOpportunity {
                token_a,
                token_b,
                input_amount: amount,
                expected_profit: profit,
                profit_percentage,
                route_a_to_b: quote_a_to_b,
                route_b_to_a: quote_b_to_a,
            })
        } else {
            Err(anyhow!("No profitable arbitrage found"))
        }
    }

    /// Execute arbitrage using flash loan + Jupiter swaps
    pub async fn execute_arbitrage_with_flash_loan(
        &self,
        opportunity: ArbitrageOpportunity,
        flash_loan_provider: FlashLoanProvider,
    ) -> Result<Signature> {
        let mut instructions = Vec::new();

        // 1. Flash loan borrow
        let flash_loan_ix = self.create_flash_loan_borrow_instruction(
            &opportunity.token_a,
            opportunity.input_amount,
            flash_loan_provider,
        )?;
        instructions.push(flash_loan_ix);

        // 2. Jupiter swap A -> B
        let swap_a_to_b = self.build_jupiter_swap_instruction(
            &opportunity.route_a_to_b
        ).await?;
        instructions.extend(swap_a_to_b);

        // 3. Jupiter swap B -> A
        let swap_b_to_a = self.build_jupiter_swap_instruction(
            &opportunity.route_b_to_a
        ).await?;
        instructions.extend(swap_b_to_a);

        // 4. Flash loan repay
        let flash_loan_repay_ix = self.create_flash_loan_repay_instruction(
            &opportunity.token_a,
            opportunity.input_amount,
            flash_loan_provider,
        )?;
        instructions.push(flash_loan_repay_ix);

        // Build and send transaction
        let transaction = Transaction::new_with_payer(&instructions, Some(&self.wallet.pubkey()));
        let signature = self.send_transaction(transaction).await?;

        Ok(signature)
    }

    async fn build_jupiter_swap_instruction(
        &self,
        quote: &QuoteResponse,
    ) -> Result<Vec<Instruction>> {
        let swap_request = SwapRequest {
            user_public_key: self.wallet.pubkey(),
            quote_response: quote.clone(),
            config: TransactionConfig {
                wrap_and_unwrap_sol: true,
                compute_unit_price_micro_lamports: Some(1000),
                ..Default::default()
            },
        };

        let swap_response = self.client.swap_instructions(&swap_request).await?;
        Ok(swap_response.instructions)
    }
}
```

### Jupiter API Endpoints

#### Production Endpoints
- **Quote API**: `https://quote-api.jup.ag/v6`
- **Swap API**: `https://quote-api.jup.ag/v6`
- **Token List**: `https://tokens.jup.ag/tokens`
- **Price API**: `https://price.jup.ag/v4/price`

#### Key Features for Arbitrage
1. **Multi-DEX Routing**: Automatically finds best routes across Raydium, Orca, Meteora, etc.
2. **Slippage Protection**: Built-in slippage tolerance and MEV protection
3. **Atomic Execution**: All swaps execute atomically or fail completely
4. **Gas Optimization**: Optimized instruction ordering and compute unit usage
5. **Real-time Pricing**: Live price feeds from multiple sources

### Jupiter Integration Best Practices

#### Rate Limiting
```rust
use tokio::time::{sleep, Duration};

pub struct JupiterRateLimiter {
    last_request: Arc<Mutex<Instant>>,
    min_interval: Duration,
}

impl JupiterRateLimiter {
    pub async fn wait_if_needed(&self) {
        let mut last = self.last_request.lock().await;
        let elapsed = last.elapsed();

        if elapsed < self.min_interval {
            let wait_time = self.min_interval - elapsed;
            sleep(wait_time).await;
        }

        *last = Instant::now();
    }
}
```

#### Error Handling and Retries
```rust
pub async fn get_quote_with_retry(
    client: &JupiterSwapApiClient,
    request: &QuoteRequest,
    max_retries: u32,
) -> Result<QuoteResponse> {
    let mut last_error = None;

    for attempt in 0..=max_retries {
        match client.quote(request).await {
            Ok(quote) => return Ok(quote),
            Err(e) => {
                last_error = Some(e);
                if attempt < max_retries {
                    let delay = Duration::from_millis(100 * (2_u64.pow(attempt)));
                    sleep(delay).await;
                }
            }
        }
    }

    Err(last_error.unwrap())
}
```

#### Price Impact Analysis
```rust
pub fn calculate_price_impact(
    quote: &QuoteResponse,
    market_price: f64,
) -> f64 {
    let execution_price = quote.out_amount as f64 / quote.in_amount as f64;
    let impact = ((market_price - execution_price) / market_price) * 100.0;
    impact.abs()
}

pub fn is_acceptable_price_impact(
    quote: &QuoteResponse,
    market_price: f64,
    max_impact_bps: u16,
) -> bool {
    let impact = calculate_price_impact(quote, market_price);
    impact <= (max_impact_bps as f64 / 100.0)
}
```

### Jupiter vs Direct DEX Integration

| Feature | Jupiter Aggregator | Direct DEX |
|---------|-------------------|------------|
| **Liquidity** | Aggregated across all DEXs | Single DEX only |
| **Price Discovery** | Best price across markets | Limited to one market |
| **Implementation** | Single API integration | Multiple integrations |
| **Route Optimization** | Automatic multi-hop | Manual route planning |
| **Slippage** | Optimized across pools | Pool-specific |
| **Gas Costs** | Optimized routing | Potentially higher |
| **Latency** | API call overhead | Direct program calls |
| **Customization** | Limited to API features | Full control |

### Recommended Integration Strategy

For flash loan arbitrage, use a **hybrid approach**:

1. **Jupiter for Discovery**: Use Jupiter Quote API to discover arbitrage opportunities
2. **Direct DEX for Execution**: Use direct program calls for time-sensitive execution
3. **Fallback to Jupiter**: Use Jupiter Swap API as fallback when direct calls fail

```rust
pub enum ExecutionStrategy {
    Direct(DirectDexRoute),
    Jupiter(JupiterRoute),
    Hybrid { primary: DirectDexRoute, fallback: JupiterRoute },
}

impl ArbitrageEngine {
    pub async fn execute_with_strategy(
        &self,
        opportunity: ArbitrageOpportunity,
        strategy: ExecutionStrategy,
    ) -> Result<Signature> {
        match strategy {
            ExecutionStrategy::Direct(route) => {
                self.execute_direct_dex(opportunity, route).await
            },
            ExecutionStrategy::Jupiter(route) => {
                self.execute_jupiter_swap(opportunity, route).await
            },
            ExecutionStrategy::Hybrid { primary, fallback } => {
                match self.execute_direct_dex(opportunity.clone(), primary).await {
                    Ok(sig) => Ok(sig),
                    Err(_) => self.execute_jupiter_swap(opportunity, fallback).await,
                }
            }
        }
    }
}
```

## Implementation Checklist

- [ ] Implement Pool trait for each DEX
- [ ] Add calculate_swap_output method to all pool implementations
- [ ] Create proper error handling for each DEX
- [ ] Implement pool discovery functions
- [ ] Add price calculation methods
- [ ] Handle different fee structures
- [ ] Implement slippage protection
- [ ] Add proper logging and monitoring

## Additional Resources

### Official Documentation
- [Raydium SDK V2 Documentation](https://raydium-io.github.io/raydium-sdk-V2/)
- [Orca Whirlpools Documentation](https://orca-so.github.io/whirlpools/)
- [Meteora DLMM SDK Documentation](https://docs.meteora.ag/dlmm-sdk/introduction)

### Anchor Framework Resources
- [Anchor Documentation](https://www.anchor-lang.com/docs) - Official Anchor framework documentation
- [Anchor GitHub Repository](https://github.com/solana-foundation/anchor) - Main Anchor framework repository
- [Anchor Book](https://book.anchor-lang.com) - Comprehensive guide and tutorials
- [Anchor Examples](https://github.com/coral-xyz/anchor/tree/master/examples) - Official example programs
- [Anchor API Documentation](https://docs.rs/anchor-lang) - Rust API documentation
- [Anchor TypeScript Client](https://coral-xyz.github.io/anchor/ts/index.html) - TypeScript client documentation

### Key Anchor Concepts for DEX Integration
- **Program Structure**: Anchor programs use a structured approach with `#[program]` modules
- **Account Validation**: Built-in account constraint validation with `#[account(...)]` attributes
- **Cross Program Invocation (CPI)**: Simplified CPI calls to other programs like DEXes
- **IDL Generation**: Automatic Interface Description Language generation for client integration
- **Error Handling**: Custom error types with `#[error_code]` for better debugging
- **Account Space Calculation**: Automatic account size calculation for rent exemption

### Anchor Client Integration Patterns
```rust
// Example: Using anchor_client for DEX interactions
use anchor_client::{
    solana_sdk::{pubkey::Pubkey, signature::Keypair},
    Client, Cluster, Program,
};

// Initialize client
let client = Client::new(Cluster::Mainnet, keypair);
let program = client.program(program_id);

// Call DEX swap instruction
let tx = program
    .request()
    .accounts(SwapAccounts {
        user: user_pubkey,
        pool: pool_pubkey,
        // ... other accounts
    })
    .args(SwapArgs {
        amount_in: 1000000,
        minimum_amount_out: 950000,
    })
    .send()?;
```

### Real-time Data Streaming & MEV Infrastructure
- [Yellowstone gRPC Geyser Plugin - QuickNode Guide](https://www.quicknode.com/guides/solana-development/tooling/geyser/yellowstone-rust)
  - Comprehensive guide for implementing real-time Solana data streaming using Yellowstone gRPC
  - Essential for building MEV-aware infrastructure and real-time price monitoring
  - Covers WebSocket connections, transaction streaming, and account updates
  - Critical for off-chain monitoring and calculations
  - Enables validator relationships for better transaction placement

### Community Resources
- [Solana Cookbook](https://solanacookbook.com/)
- [Solana Program Library](https://spl.solana.com/)
- [Anchor Discord Community](https://discord.gg/NHHGSXAnXk)
- [Solana Stack Exchange](https://solana.stackexchange.com/)
