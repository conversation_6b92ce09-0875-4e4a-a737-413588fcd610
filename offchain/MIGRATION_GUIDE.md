# Migration Guide: Lock-Free Concurrency Improvements

This guide explains how to migrate from the old mutex-based global state to the new lock-free implementation.

## Overview of Changes

### 1. Global State Management
**Old (Deprecated):**
```rust
use crate::engine::globals::{TOKEN_TRACKING, BUYING_ENABLED, MAX_WAIT_TIME};

// Accessing with mutex locks
{
    let mut tracking = TOKEN_TRACKING.lock().unwrap();
    tracking.insert(mint, info);
}

let buying_enabled = *BUYING_ENABLED.lock().unwrap();
```

**New (Recommended):**
```rust
use crate::core::app_state::AppState;
use crate::core::lock_free_globals::LockFreeGlobals;

// Access through AppState
app_state.globals.update_token_tracking(mint, info);
let buying_enabled = app_state.globals.is_buying_enabled();

// Or direct access to lock-free globals
use crate::engine::globals::LOCK_FREE_GLOBALS;
LOCK_FREE_GLOBALS.update_token_tracking(mint, info);
```

### 2. RPC Operations
**Old:**
```rust
let rpc_client = RpcClient::new(env::var("RPC_HTTP").unwrap());
let result = rpc_client.get_account(&pubkey).await?;
```

**New (With Protection):**
```rust
let result = app_state.execute_rpc_protected(|client| async move {
    client.get_account(&pubkey).await
        .map_err(|e| anyhow::anyhow!("RPC error: {}", e))
}).await?;
```

### 3. Swap Operations
**Old:**
```rust
// Direct swap execution without protection
let signature = connection.send_transaction(&tx).await?;
```

**New (With Protection):**
```rust
let signature = app_state.execute_swap_protected(|| async {
    connection.send_transaction(&tx).await
        .map_err(|e| anyhow::anyhow!("Swap error: {}", e))
}).await?;
```

### 4. Price Checks
**Old:**
```rust
// Unlimited concurrent price checks
for token in tokens {
    tokio::spawn(async move {
        let price = get_token_price(&token).await?;
        // Process price...
    });
}
```

**New (Rate Limited):**
```rust
for token in tokens {
    let app_state = app_state.clone();
    tokio::spawn(async move {
        let price = app_state.execute_price_check_protected(|| async {
            get_token_price(&token).await
        }).await?;
        // Process price...
    });
}
```

## Migration Steps

### Step 1: Update Dependencies
Ensure your `Cargo.toml` includes the new dependencies:
```toml
dashmap = "6.1.0"
arc-swap = "1.7.1"
deadpool = { version = "0.12.1", features = ["managed", "rt_tokio_1"] }
prometheus = { version = "0.13.4", features = ["process"] }
thiserror = "1.0.63"
async-trait = "0.1.83"
```

### Step 2: Initialize AppState
Replace manual component initialization with AppState:

**Old:**
```rust
fn main() {
    let keypair = load_keypair();
    let rpc_client = RpcClient::new(rpc_url);
    // Manual setup...
}
```

**New:**
```rust
#[tokio::main]
async fn main() -> Result<()> {
    let keypair = load_keypair();
    let rpc_url = env::var("RPC_HTTP")?;
    
    let app_state = AppState::from_env(keypair, rpc_url).await?;
    
    // Use app_state throughout your application
    run_arbitrage_bot(app_state).await
}
```

### Step 3: Update Token Tracking
Replace mutex-based token tracking:

**Old:**
```rust
{
    let mut tracking = TOKEN_TRACKING.lock().unwrap();
    if let Some(info) = tracking.get_mut(&mint) {
        info.top_pnl = new_pnl;
    }
}
```

**New:**
```rust
if let Some(mut info) = app_state.globals.get_token_tracking(&mint) {
    info.update_price(new_price);
    app_state.globals.update_token_tracking(mint, info);
}
```

### Step 4: Update Configuration Access
Replace direct global access:

**Old:**
```rust
let max_wait = *MAX_WAIT_TIME.lock().unwrap();
```

**New:**
```rust
let config = app_state.globals.get_config();
let max_wait = config.max_wait_time_ms;
```

### Step 5: Add Health Monitoring
Implement health checks and monitoring:

```rust
// Periodic health checks
tokio::spawn(async move {
    let mut interval = tokio::time::interval(Duration::from_secs(30));
    loop {
        interval.tick().await;
        
        let health = app_state.health_check().await;
        if !health.healthy {
            tracing::warn!("Health check failed: {}", health.status_string());
        }
        
        let status = app_state.get_system_status();
        tracing::info!("System status: {}", status.global_stats.status_string());
    }
});
```

## Environment Variables

Update your `.env` file with the new configuration options:

```bash
# Rate limiting
MAX_CONCURRENT_RPC_CALLS=15
MAX_CONCURRENT_SWAPS=3
MAX_CONCURRENT_PRICE_CHECKS=25

# Circuit breakers
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60

# Retry logic
MAX_RETRY_ATTEMPTS=3
RETRY_BASE_DELAY_MS=250
RETRY_MAX_DELAY_MS=10000
RETRY_BACKOFF_MULTIPLIER=2.0

# Connection pooling
RPC_POOL_MAX_SIZE=20
RPC_POOL_MIN_SIZE=5
RPC_POOL_TIMEOUT_SECONDS=15

# Trading configuration
DEFAULT_TRADE_AMOUNT=0.05
MAX_SLIPPAGE_BPS=100
MIN_PROFIT_THRESHOLD_BPS=75
MAX_POSITION_HOLD_TIME_MS=300000
MAX_TOTAL_POSITION_SIZE_SOL=1.0

# Emergency controls
EMERGENCY_STOP=false
ENABLE_EMERGENCY_LIQUIDATION=true

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_LEVEL=INFO
```

## Common Patterns

### Pattern 1: Safe Token Operations
```rust
// Check if we can buy before attempting
if app_state.globals.is_buying_enabled() && 
   app_state.globals.can_add_position(trade_amount) {
    
    let result = app_state.execute_swap_protected(|| async {
        // Execute swap logic
        execute_buy_order(mint, amount).await
    }).await?;
    
    // Update tracking
    app_state.globals.mark_token_bought(mint, price, amount);
}
```

### Pattern 2: Batch Price Updates
```rust
// Get all tokens that need price updates
let tokens_to_check = app_state.globals.get_all_tracked_tokens()
    .into_iter()
    .filter(|(_, info)| should_update_price(info))
    .map(|(mint, _)| mint)
    .collect::<Vec<_>>();

// Process in batches with rate limiting
for chunk in tokens_to_check.chunks(10) {
    let futures: Vec<_> = chunk.iter().map(|mint| {
        let app_state = app_state.clone();
        let mint = mint.clone();
        async move {
            app_state.execute_price_check_protected(|| async {
                get_token_price(&mint).await
            }).await
        }
    }).collect();
    
    let prices = futures::future::join_all(futures).await;
    // Process results...
}
```

### Pattern 3: Emergency Handling
```rust
// Monitor for emergency conditions
if app_state.globals.get_stats().is_under_stress() {
    tracing::warn!("System under stress, reducing activity");
    app_state.globals.set_buying_enabled(false);
    
    // Liquidate positions if needed
    let config = app_state.globals.get_config();
    if config.enable_emergency_liquidation {
        emergency_liquidate_positions(&app_state).await?;
    }
}
```

## Testing

Test your migration with the new components:

```rust
#[tokio::test]
async fn test_app_state_integration() {
    let keypair = Keypair::new();
    let rpc_url = "https://api.devnet.solana.com".to_string();
    
    let app_state = AppState::new(keypair, rpc_url).await.unwrap();
    
    // Test rate limiting
    let result = app_state.execute_rpc_protected(|client| async move {
        client.get_latest_blockhash().await
            .map_err(|e| anyhow::anyhow!("RPC error: {}", e))
    }).await;
    
    assert!(result.is_ok());
}
```

## Performance Benefits

After migration, you should see:

1. **Reduced Lock Contention**: No more blocking on mutex locks
2. **Better Throughput**: Rate limiting prevents resource exhaustion
3. **Improved Reliability**: Circuit breakers prevent cascade failures
4. **Automatic Recovery**: Retry logic handles transient failures
5. **Better Monitoring**: Comprehensive metrics and health checks

## Rollback Plan

If issues arise, you can temporarily revert to the old system by:

1. Using the deprecated globals directly
2. Commenting out the new AppState usage
3. Reverting to direct RPC client usage

The old code paths are preserved for backward compatibility during the migration period.
