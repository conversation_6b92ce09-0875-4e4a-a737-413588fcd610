//! Direct DEX integration for flash loan receiver
//! 
//! This module provides utilities for direct integration with various DEXs
//! for high-speed arbitrage execution without aggregator overhead.

use anchor_lang::prelude::*;
use anchor_lang::solana_program::{
    instruction::{Instruction, AccountMeta},
    pubkey::Pubkey,
};
use crate::error::FlashLoanError;

/// Supported DEX programs
pub mod dex_programs {
    use super::*;
    
    /// Raydium AMM program ID
    pub const RAYDIUM_AMM: &str = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8";
    
    /// Raydium CLMM program ID  
    pub const RAYDIUM_CLMM: &str = "CAMMCzo5YL8w4VFF8KVHrK22GGUQpMkFr9WeqATV9Uu";
    
    /// Orca Whirlpools program ID
    pub const ORCA_WHIRLPOOLS: &str = "whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc";
    
    /// Meteora DLMM program ID
    pub const METEORA_DLMM: &str = "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo";
    
    /// Serum DEX program ID
    pub const SERUM_DEX: &str = "9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin";
}

/// DEX instruction discriminators
pub mod discriminators {
    /// Raydium AMM swap instruction
    pub const RAYDIUM_SWAP: [u8; 8] = [0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00];
    
    /// Orca Whirlpool swap instruction
    pub const ORCA_SWAP: [u8; 8] = [0xf8, 0xc6, 0x9e, 0x91, 0xe1, 0x75, 0x87, 0xc8];
    
    /// Meteora DLMM swap instruction
    pub const METEORA_SWAP: [u8; 8] = [0x14, 0x1b, 0x9a, 0x38, 0x1c, 0xf0, 0x6c, 0x51];
    
    /// Serum new order instruction
    pub const SERUM_NEW_ORDER: [u8; 8] = [0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00];
}

/// DEX swap parameters
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct DexSwapParams {
    /// DEX program ID
    pub program_id: Pubkey,
    /// Pool or market account
    pub pool_account: Pubkey,
    /// Input amount
    pub amount_in: u64,
    /// Minimum output amount
    pub amount_out_min: u64,
    /// Additional accounts required by the DEX
    pub additional_accounts: Vec<Pubkey>,
    /// Instruction discriminator
    pub discriminator: [u8; 8],
}

/// Raydium AMM swap builder
pub mod raydium {
    use super::*;
    
    /// Build Raydium AMM swap instruction
    pub fn build_swap_instruction(
        user: &Pubkey,
        source_token: &Pubkey,
        dest_token: &Pubkey,
        pool_id: &Pubkey,
        amount_in: u64,
        amount_out_min: u64,
    ) -> Result<Instruction> {
        let program_id: Pubkey = dex_programs::RAYDIUM_AMM.parse()
            .map_err(|_| FlashLoanError::InvalidDexProgram)?;
        
        // Build instruction data
        let mut data = discriminators::RAYDIUM_SWAP.to_vec();
        data.extend_from_slice(&amount_in.to_le_bytes());
        data.extend_from_slice(&amount_out_min.to_le_bytes());
        
        // Build accounts
        let accounts = vec![
            AccountMeta::new(*pool_id, false),
            AccountMeta::new_readonly(anchor_lang::system_program::ID, false),
            AccountMeta::new(*user, true),
            AccountMeta::new(*source_token, false),
            AccountMeta::new(*dest_token, false),
            AccountMeta::new_readonly(spl_token::id(), false),
        ];
        
        Ok(Instruction {
            program_id,
            accounts,
            data,
        })
    }
    
    /// Validate Raydium pool account
    pub fn validate_pool_account(pool_account: &AccountInfo) -> Result<()> {
        // Basic validation - in production would check pool state
        require!(pool_account.data_len() > 0, FlashLoanError::AccountValidationFailed);
        Ok(())
    }
}

/// Orca Whirlpools swap builder
pub mod orca {
    use super::*;
    
    /// Build Orca Whirlpool swap instruction
    pub fn build_swap_instruction(
        user: &Pubkey,
        source_token: &Pubkey,
        dest_token: &Pubkey,
        whirlpool: &Pubkey,
        amount_in: u64,
        amount_out_min: u64,
        a_to_b: bool,
    ) -> Result<Instruction> {
        let program_id: Pubkey = dex_programs::ORCA_WHIRLPOOLS.parse()
            .map_err(|_| FlashLoanError::InvalidDexProgram)?;
        
        // Build instruction data
        let mut data = discriminators::ORCA_SWAP.to_vec();
        data.extend_from_slice(&amount_in.to_le_bytes());
        data.extend_from_slice(&amount_out_min.to_le_bytes());
        data.push(if a_to_b { 1 } else { 0 });
        
        // Build accounts
        let accounts = vec![
            AccountMeta::new(*whirlpool, false),
            AccountMeta::new(*user, true),
            AccountMeta::new(*source_token, false),
            AccountMeta::new(*dest_token, false),
            AccountMeta::new_readonly(spl_token::id(), false),
        ];
        
        Ok(Instruction {
            program_id,
            accounts,
            data,
        })
    }
    
    /// Calculate swap direction for Orca
    pub fn calculate_swap_direction(
        input_mint: &Pubkey,
        output_mint: &Pubkey,
        token_a: &Pubkey,
        token_b: &Pubkey,
    ) -> Result<bool> {
        if input_mint == token_a && output_mint == token_b {
            Ok(true) // a_to_b
        } else if input_mint == token_b && output_mint == token_a {
            Ok(false) // b_to_a
        } else {
            Err(FlashLoanError::InvalidSwapAmount.into())
        }
    }
}

/// Meteora DLMM swap builder
pub mod meteora {
    use super::*;
    
    /// Build Meteora DLMM swap instruction
    pub fn build_swap_instruction(
        user: &Pubkey,
        source_token: &Pubkey,
        dest_token: &Pubkey,
        lb_pair: &Pubkey,
        amount_in: u64,
        amount_out_min: u64,
    ) -> Result<Instruction> {
        let program_id: Pubkey = dex_programs::METEORA_DLMM.parse()
            .map_err(|_| FlashLoanError::InvalidDexProgram)?;
        
        // Build instruction data
        let mut data = discriminators::METEORA_SWAP.to_vec();
        data.extend_from_slice(&amount_in.to_le_bytes());
        data.extend_from_slice(&amount_out_min.to_le_bytes());
        
        // Build accounts
        let accounts = vec![
            AccountMeta::new(*lb_pair, false),
            AccountMeta::new(*user, true),
            AccountMeta::new(*source_token, false),
            AccountMeta::new(*dest_token, false),
            AccountMeta::new_readonly(spl_token::id(), false),
        ];
        
        Ok(Instruction {
            program_id,
            accounts,
            data,
        })
    }
    
    /// Validate Meteora LB pair
    pub fn validate_lb_pair(lb_pair_account: &AccountInfo) -> Result<()> {
        require!(lb_pair_account.data_len() > 0, FlashLoanError::AccountValidationFailed);
        Ok(())
    }
}

/// Serum DEX swap builder
pub mod serum {
    use super::*;
    
    /// Build Serum new order instruction
    pub fn build_new_order_instruction(
        user: &Pubkey,
        source_token: &Pubkey,
        dest_token: &Pubkey,
        market: &Pubkey,
        amount_in: u64,
        amount_out_min: u64,
    ) -> Result<Instruction> {
        let program_id: Pubkey = dex_programs::SERUM_DEX.parse()
            .map_err(|_| FlashLoanError::InvalidDexProgram)?;
        
        // Build instruction data
        let mut data = discriminators::SERUM_NEW_ORDER.to_vec();
        data.extend_from_slice(&amount_in.to_le_bytes());
        data.extend_from_slice(&amount_out_min.to_le_bytes());
        
        // Build accounts
        let accounts = vec![
            AccountMeta::new(*market, false),
            AccountMeta::new(*user, true),
            AccountMeta::new(*source_token, false),
            AccountMeta::new(*dest_token, false),
            AccountMeta::new_readonly(spl_token::id(), false),
        ];
        
        Ok(Instruction {
            program_id,
            accounts,
            data,
        })
    }
}

/// DEX selection and routing utilities
pub mod routing {
    use super::*;
    
    /// Select best DEX for a given token pair
    pub fn select_best_dex(
        input_mint: &Pubkey,
        output_mint: &Pubkey,
        amount: u64,
    ) -> Result<String> {
        // Simple selection logic - in production would check liquidity
        if amount > 1_000_000_000 { // > 1 SOL
            Ok(dex_programs::RAYDIUM_AMM.to_string())
        } else if amount > 100_000_000 { // > 0.1 SOL
            Ok(dex_programs::ORCA_WHIRLPOOLS.to_string())
        } else {
            Ok(dex_programs::METEORA_DLMM.to_string())
        }
    }
    
    /// Calculate optimal route for arbitrage
    pub fn calculate_arbitrage_route(
        token_a: &Pubkey,
        token_b: &Pubkey,
        amount: u64,
    ) -> Result<Vec<DexSwapParams>> {
        let dex1 = select_best_dex(token_a, token_b, amount)?;
        let dex2 = select_best_dex(token_b, token_a, amount)?;
        
        let route = vec![
            DexSwapParams {
                program_id: dex1.parse().map_err(|_| FlashLoanError::InvalidDexProgram)?,
                pool_account: Pubkey::default(), // Would be resolved from pool data
                amount_in: amount,
                amount_out_min: amount.saturating_mul(95).saturating_div(100),
                additional_accounts: vec![],
                discriminator: discriminators::RAYDIUM_SWAP,
            },
            DexSwapParams {
                program_id: dex2.parse().map_err(|_| FlashLoanError::InvalidDexProgram)?,
                pool_account: Pubkey::default(), // Would be resolved from pool data
                amount_in: 0, // Will be set based on first swap output
                amount_out_min: amount.saturating_mul(101).saturating_div(100), // Expect profit
                additional_accounts: vec![],
                discriminator: discriminators::ORCA_SWAP,
            },
        ];
        
        Ok(route)
    }
}

/// DEX integration utilities
pub mod utils {
    use super::*;
    
    /// Validate DEX program ID
    pub fn validate_dex_program_id(program_id: &Pubkey) -> Result<()> {
        let valid_programs = [
            dex_programs::RAYDIUM_AMM,
            dex_programs::RAYDIUM_CLMM,
            dex_programs::ORCA_WHIRLPOOLS,
            dex_programs::METEORA_DLMM,
            dex_programs::SERUM_DEX,
        ];
        
        let program_str = program_id.to_string();
        require!(
            valid_programs.contains(&program_str.as_str()),
            FlashLoanError::InvalidDexProgram
        );
        
        Ok(())
    }
    
    /// Estimate gas cost for DEX swap
    pub fn estimate_dex_gas_cost(dex_program: &str) -> Result<u64> {
        match dex_program {
            dex_programs::RAYDIUM_AMM => Ok(30_000),
            dex_programs::RAYDIUM_CLMM => Ok(40_000),
            dex_programs::ORCA_WHIRLPOOLS => Ok(35_000),
            dex_programs::METEORA_DLMM => Ok(45_000),
            dex_programs::SERUM_DEX => Ok(50_000),
            _ => Err(FlashLoanError::InvalidDexProgram.into()),
        }
    }
    
    /// Check if DEX supports token pair
    pub fn supports_token_pair(
        dex_program: &str,
        token_a: &Pubkey,
        token_b: &Pubkey,
    ) -> Result<bool> {
        // Basic validation - in production would check actual pool existence
        require!(token_a != token_b, FlashLoanError::SameMintArbitrage);
        
        match dex_program {
            dex_programs::RAYDIUM_AMM |
            dex_programs::RAYDIUM_CLMM |
            dex_programs::ORCA_WHIRLPOOLS |
            dex_programs::METEORA_DLMM |
            dex_programs::SERUM_DEX => Ok(true),
            _ => Ok(false),
        }
    }
}
