//! Error definitions for the flash loan receiver program

use anchor_lang::prelude::*;

#[error_code]
pub enum FlashLoanError {
    #[msg("Invalid loan amount")]
    InvalidLoanAmount,
    
    #[msg("Loan amount too large")]
    LoanAmountTooLarge,
    
    #[msg("Insufficient liquidity")]
    InsufficientLiquidity,
    
    #[msg("Invalid destination owner")]
    InvalidDestinationOwner,
    
    #[msg("Mint mismatch between accounts")]
    MintMismatch,
    
    #[msg("Destination account not empty")]
    DestinationNotEmpty,
    
    #[msg("Math overflow")]
    MathOverflow,
    
    #[msg("Fee too large")]
    FeeTooLarge,
    
    #[msg("Insufficient repayment balance")]
    InsufficientRepaymentBalance,
    
    #[msg("Invalid Jupiter program")]
    InvalidJupiterProgram,
    
    #[msg("Invalid swap data")]
    InvalidSwapData,
    
    #[msg("Swap data too large")]
    SwapDataTooLarge,
    
    #[msg("Invalid token account owner")]
    InvalidTokenAccountOwner,
    
    #[msg("Swap execution failed")]
    SwapExecutionFailed,
    
    #[msg("No tokens swapped")]
    NoTokensSwapped,
    
    #[msg("No tokens received")]
    NoTokensReceived,
    
    #[msg("Same mint arbitrage not allowed")]
    SameMintArbitrage,
    
    #[msg("Invalid swap amount")]
    InvalidSwapAmount,
    
    #[msg("Invalid minimum out amount")]
    InvalidMinimumOut,
    
    #[msg("Excessive slippage")]
    ExcessiveSlippage,
    
    #[msg("Invalid DEX program")]
    InvalidDexProgram,
    
    #[msg("Insufficient balance")]
    InsufficientBalance,
    
    #[msg("Slippage exceeded")]
    SlippageExceeded,
    
    #[msg("Invalid repayment amount")]
    InvalidRepaymentAmount,
    
    #[msg("Invalid fee amount")]
    InvalidFeeAmount,
    
    #[msg("Invalid account ownership")]
    InvalidAccountOwnership,
    
    #[msg("Repayment validation failed")]
    RepaymentValidationFailed,
    
    #[msg("Excessive fee")]
    ExcessiveFee,
    
    #[msg("Insufficient profit")]
    InsufficientProfit,
    
    #[msg("Flash loan execution timeout")]
    ExecutionTimeout,
    
    #[msg("Invalid flash loan state")]
    InvalidFlashLoanState,
    
    #[msg("Flash loan not active")]
    FlashLoanNotActive,
    
    #[msg("Unauthorized access")]
    UnauthorizedAccess,
    
    #[msg("Invalid program authority")]
    InvalidProgramAuthority,
    
    #[msg("Account validation failed")]
    AccountValidationFailed,
    
    #[msg("Instruction validation failed")]
    InstructionValidationFailed,
    
    #[msg("Price impact too high")]
    PriceImpactTooHigh,
    
    #[msg("Market conditions unfavorable")]
    MarketConditionsUnfavorable,
    
    #[msg("Arbitrage opportunity expired")]
    ArbitrageOpportunityExpired,
    
    #[msg("Risk threshold exceeded")]
    RiskThresholdExceeded,
}
