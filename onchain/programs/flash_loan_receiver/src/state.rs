//! State definitions for the flash loan receiver program

use anchor_lang::prelude::*;

/// Flash loan state tracking
#[account]
#[derive(Debug)]
pub struct FlashLoanState {
    /// Amount of the flash loan
    pub loan_amount: u64,
    
    /// Timestamp when loan was borrowed
    pub borrowed_at: i64,
    
    /// Token mint of the borrowed asset
    pub source_mint: Pubkey,
    
    /// Destination account holding the borrowed funds
    pub destination_account: Pubkey,
    
    /// Lending market authority
    pub lending_market_authority: Pubkey,
    
    /// Whether the flash loan is currently active
    pub is_active: bool,
    
    /// Expected fee for repayment
    pub expected_fee: u64,
    
    /// Maximum execution time allowed (seconds)
    pub max_execution_time: i64,
    
    /// Arbitrage strategy being executed
    pub strategy_type: StrategyType,
    
    /// Reserved space for future upgrades
    pub _reserved: [u8; 64],
}

impl FlashLoanState {
    pub const LEN: usize = 8 + // discriminator
        8 + // loan_amount
        8 + // borrowed_at
        32 + // source_mint
        32 + // destination_account
        32 + // lending_market_authority
        1 + // is_active
        8 + // expected_fee
        8 + // max_execution_time
        1 + // strategy_type
        64; // _reserved
}

/// Arbitrage strategy types
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Copy, Debug, PartialEq)]
pub enum StrategyType {
    /// Jupiter aggregator arbitrage
    Jupiter = 0,
    /// Direct DEX arbitrage
    DirectDex = 1,
    /// Multi-hop arbitrage
    MultiHop = 2,
    /// Triangle arbitrage
    Triangle = 3,
}

impl Default for StrategyType {
    fn default() -> Self {
        StrategyType::Jupiter
    }
}

/// Arbitrage execution parameters
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct ArbitrageParams {
    /// Input token mint
    pub input_mint: Pubkey,
    
    /// Output token mint
    pub output_mint: Pubkey,
    
    /// Amount to trade
    pub amount: u64,
    
    /// Minimum expected output
    pub minimum_out: u64,
    
    /// Maximum slippage tolerance (basis points)
    pub max_slippage_bps: u16,
    
    /// DEX preference for execution
    pub dex_preference: DexPreference,
    
    /// Maximum price impact tolerance (basis points)
    pub max_price_impact_bps: u16,
}

/// DEX preference for arbitrage execution
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Copy, Debug, PartialEq)]
pub enum DexPreference {
    /// Use Jupiter aggregator
    Jupiter = 0,
    /// Use Raydium directly
    Raydium = 1,
    /// Use Orca directly
    Orca = 2,
    /// Use Meteora directly
    Meteora = 3,
    /// Use Serum directly
    Serum = 4,
    /// Auto-select best option
    Auto = 5,
}

impl Default for DexPreference {
    fn default() -> Self {
        DexPreference::Auto
    }
}

/// Risk management parameters
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RiskParams {
    /// Maximum loan amount (lamports)
    pub max_loan_amount: u64,
    
    /// Minimum profit threshold (lamports)
    pub min_profit_threshold: u64,
    
    /// Maximum execution time (seconds)
    pub max_execution_time: i64,
    
    /// Maximum fee tolerance (basis points)
    pub max_fee_bps: u16,
    
    /// Enable emergency stop
    pub emergency_stop: bool,
}

impl Default for RiskParams {
    fn default() -> Self {
        Self {
            max_loan_amount: 1_000_000_000, // 1 SOL
            min_profit_threshold: 10_000, // 0.01 SOL
            max_execution_time: 30, // 30 seconds
            max_fee_bps: 50, // 0.5%
            emergency_stop: false,
        }
    }
}

/// Flash loan execution context
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct ExecutionContext {
    /// Flash loan state
    pub flash_loan_state: FlashLoanState,
    
    /// Arbitrage parameters
    pub arbitrage_params: ArbitrageParams,
    
    /// Risk management parameters
    pub risk_params: RiskParams,
    
    /// Execution start timestamp
    pub execution_start: i64,
    
    /// Current execution step
    pub current_step: ExecutionStep,
}

/// Execution steps for flash loan arbitrage
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Copy, Debug, PartialEq)]
pub enum ExecutionStep {
    /// Flash loan received
    LoanReceived = 0,
    /// Arbitrage execution started
    ArbitrageStarted = 1,
    /// First swap completed
    FirstSwapCompleted = 2,
    /// Second swap completed
    SecondSwapCompleted = 3,
    /// Profit calculated
    ProfitCalculated = 4,
    /// Repayment initiated
    RepaymentInitiated = 5,
    /// Execution completed
    ExecutionCompleted = 6,
    /// Execution failed
    ExecutionFailed = 7,
}

impl Default for ExecutionStep {
    fn default() -> Self {
        ExecutionStep::LoanReceived
    }
}
