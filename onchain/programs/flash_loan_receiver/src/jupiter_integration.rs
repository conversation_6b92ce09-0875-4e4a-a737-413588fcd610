//! Jupiter aggregator integration for flash loan receiver
//! 
//! This module provides utilities for integrating with Jupiter's swap aggregator
//! within the flash loan receiver program for optimal arbitrage execution.

use anchor_lang::prelude::*;
use anchor_lang::solana_program::{
    instruction::{Instruction, AccountMeta},
    pubkey::Pubkey,
};
use crate::error::FlashLoanError;

/// Jupiter program ID
pub const JUPITER_PROGRAM_ID: &str = "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4";

/// Jupiter swap instruction discriminator
pub const JUPITER_SWAP_DISCRIMINATOR: [u8; 8] = [0xe4, 0x45, 0xa5, 0x2e, 0x51, 0xcb, 0x9a, 0x1d];

/// Jupiter route step for multi-hop swaps
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct JupiterRouteStep {
    /// DEX program ID for this step
    pub program_id: Pubkey,
    /// Input mint for this step
    pub input_mint: Pubkey,
    /// Output mint for this step
    pub output_mint: Pubkey,
    /// Amount in for this step
    pub amount_in: u64,
    /// Minimum amount out for this step
    pub amount_out_min: u64,
    /// Additional accounts needed for this step
    pub accounts: Vec<Pubkey>,
}

/// Jupiter swap parameters
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct JupiterSwapParams {
    /// Route steps for the swap
    pub route_steps: Vec<JupiterRouteStep>,
    /// Total input amount
    pub amount_in: u64,
    /// Minimum total output amount
    pub amount_out_min: u64,
    /// Platform fee (basis points)
    pub platform_fee_bps: u8,
}

/// Build Jupiter swap instruction from swap parameters
pub fn build_jupiter_swap_instruction(
    user: &Pubkey,
    source_token_account: &Pubkey,
    destination_token_account: &Pubkey,
    swap_params: &JupiterSwapParams,
) -> Result<Instruction> {
    // Validate Jupiter program ID
    let jupiter_program_id: Pubkey = JUPITER_PROGRAM_ID.parse()
        .map_err(|_| FlashLoanError::InvalidJupiterProgram)?;
    
    // Build instruction data
    let mut data = JUPITER_SWAP_DISCRIMINATOR.to_vec();
    
    // Serialize swap parameters
    let serialized_params = swap_params.try_to_vec()
        .map_err(|_| FlashLoanError::InvalidSwapData)?;
    data.extend_from_slice(&serialized_params);
    
    // Build base accounts
    let mut accounts = vec![
        AccountMeta::new(*user, true),
        AccountMeta::new(*source_token_account, false),
        AccountMeta::new(*destination_token_account, false),
        AccountMeta::new_readonly(spl_token::id(), false),
        AccountMeta::new_readonly(anchor_lang::system_program::ID, false),
    ];
    
    // Add route-specific accounts
    for step in &swap_params.route_steps {
        accounts.push(AccountMeta::new_readonly(step.program_id, false));
        for account in &step.accounts {
            accounts.push(AccountMeta::new(*account, false));
        }
    }
    
    Ok(Instruction {
        program_id: jupiter_program_id,
        accounts,
        data,
    })
}

/// Parse Jupiter quote response into swap parameters
pub fn parse_jupiter_quote_to_params(
    quote_data: &[u8],
) -> Result<JupiterSwapParams> {
    // This would parse actual Jupiter quote response
    // For now, return a basic structure
    
    if quote_data.len() < 32 {
        return Err(FlashLoanError::InvalidSwapData.into());
    }
    
    // Parse basic parameters from quote data
    let amount_in = u64::from_le_bytes(
        quote_data[0..8].try_into()
            .map_err(|_| FlashLoanError::InvalidSwapData)?
    );
    
    let amount_out_min = u64::from_le_bytes(
        quote_data[8..16].try_into()
            .map_err(|_| FlashLoanError::InvalidSwapData)?
    );
    
    // Create basic route step (would be more complex in production)
    let route_step = JupiterRouteStep {
        program_id: Pubkey::default(), // Would be parsed from quote
        input_mint: Pubkey::default(),
        output_mint: Pubkey::default(),
        amount_in,
        amount_out_min,
        accounts: vec![],
    };
    
    Ok(JupiterSwapParams {
        route_steps: vec![route_step],
        amount_in,
        amount_out_min,
        platform_fee_bps: 0,
    })
}

/// Validate Jupiter swap parameters
pub fn validate_jupiter_swap_params(
    params: &JupiterSwapParams,
) -> Result<()> {
    // Validate basic parameters
    require!(params.amount_in > 0, FlashLoanError::InvalidSwapAmount);
    require!(params.amount_out_min > 0, FlashLoanError::InvalidMinimumOut);
    require!(!params.route_steps.is_empty(), FlashLoanError::InvalidSwapData);
    
    // Validate platform fee
    require!(params.platform_fee_bps <= 1000, FlashLoanError::ExcessiveFee); // Max 10%
    
    // Validate route steps
    for step in &params.route_steps {
        require!(step.amount_in > 0, FlashLoanError::InvalidSwapAmount);
        require!(step.amount_out_min > 0, FlashLoanError::InvalidMinimumOut);
        require!(step.input_mint != step.output_mint, FlashLoanError::SameMintArbitrage);
    }
    
    Ok(())
}

/// Calculate expected output from Jupiter route
pub fn calculate_expected_output(
    params: &JupiterSwapParams,
) -> Result<u64> {
    let mut current_amount = params.amount_in;
    
    for step in &params.route_steps {
        // Simple calculation - in production would use actual price data
        current_amount = step.amount_out_min;
    }
    
    // Apply platform fee
    let fee_amount = (current_amount as u128)
        .checked_mul(params.platform_fee_bps as u128)
        .ok_or(FlashLoanError::MathOverflow)?
        .checked_div(10_000)
        .ok_or(FlashLoanError::MathOverflow)?;
    
    let final_amount = current_amount
        .checked_sub(fee_amount as u64)
        .ok_or(FlashLoanError::MathOverflow)?;
    
    Ok(final_amount)
}

/// Estimate gas cost for Jupiter swap
pub fn estimate_jupiter_gas_cost(
    params: &JupiterSwapParams,
) -> Result<u64> {
    // Base cost for Jupiter swap
    let mut gas_cost = 50_000u64; // Base compute units
    
    // Add cost per route step
    gas_cost = gas_cost
        .checked_add(params.route_steps.len() as u64 * 20_000)
        .ok_or(FlashLoanError::MathOverflow)?;
    
    // Add cost per additional account
    let total_accounts: usize = params.route_steps.iter()
        .map(|step| step.accounts.len())
        .sum();
    
    gas_cost = gas_cost
        .checked_add(total_accounts as u64 * 1_000)
        .ok_or(FlashLoanError::MathOverflow)?;
    
    Ok(gas_cost)
}

/// Check if Jupiter route is profitable for arbitrage
pub fn is_jupiter_route_profitable(
    params: &JupiterSwapParams,
    flash_loan_fee: u64,
    gas_cost_lamports: u64,
    min_profit_lamports: u64,
) -> Result<bool> {
    let expected_output = calculate_expected_output(params)?;
    
    let total_costs = flash_loan_fee
        .checked_add(gas_cost_lamports)
        .ok_or(FlashLoanError::MathOverflow)?;
    
    let net_profit = expected_output
        .checked_sub(params.amount_in)
        .and_then(|profit| profit.checked_sub(total_costs))
        .unwrap_or(0);
    
    Ok(net_profit >= min_profit_lamports)
}

/// Jupiter integration utilities
pub mod utils {
    use super::*;
    
    /// Get Jupiter program account info
    pub fn get_jupiter_program_id() -> Result<Pubkey> {
        JUPITER_PROGRAM_ID.parse()
            .map_err(|_| FlashLoanError::InvalidJupiterProgram.into())
    }
    
    /// Validate Jupiter program account
    pub fn validate_jupiter_program(program_account: &AccountInfo) -> Result<()> {
        let expected_id = get_jupiter_program_id()?;
        require!(
            program_account.key() == expected_id,
            FlashLoanError::InvalidJupiterProgram
        );
        Ok(())
    }
    
    /// Create minimal Jupiter swap instruction for testing
    pub fn create_test_jupiter_instruction(
        user: &Pubkey,
        source: &Pubkey,
        destination: &Pubkey,
        amount: u64,
    ) -> Result<Instruction> {
        let params = JupiterSwapParams {
            route_steps: vec![JupiterRouteStep {
                program_id: get_jupiter_program_id()?,
                input_mint: *source,
                output_mint: *destination,
                amount_in: amount,
                amount_out_min: amount.saturating_mul(95).saturating_div(100), // 5% slippage
                accounts: vec![],
            }],
            amount_in: amount,
            amount_out_min: amount.saturating_mul(95).saturating_div(100),
            platform_fee_bps: 0,
        };
        
        build_jupiter_swap_instruction(user, source, destination, &params)
    }
}
