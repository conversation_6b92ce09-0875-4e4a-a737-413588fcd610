//! SPL-Compliant Flash Loan Receiver Program
//! 
//! This program implements the SPL flash loan receiver interface for atomic arbitrage execution.
//! It receives flash loans from various providers (Solend, Mango, Marginfi, Kamino) and executes
//! arbitrage operations before repaying the loan within the same transaction.

use anchor_lang::prelude::*;
use anchor_spl::token::{self, Token, TokenAccount, Transfer};
use spl_token_lending::instruction::LendingInstruction;

declare_id!("FLRcv1234567890123456789012345678901234567890");

pub mod instructions;
pub mod state;
pub mod error;
pub mod jupiter_integration;
pub mod dex_integration;

use instructions::*;
use error::*;

#[program]
pub mod flash_loan_receiver {
    use super::*;

    /// SPL-compliant flash loan receiver instruction
    /// Must use instruction discriminator 0 as per SPL specification
    #[instruction(0)]
    pub fn receive_flash_loan(
        ctx: Context<ReceiveFlashLoan>,
        amount: u64,
    ) -> Result<()> {
        instructions::receive_flash_loan::handler(ctx, amount)
    }

    /// Execute arbitrage using Jupiter aggregator
    pub fn execute_jupiter_arbitrage(
        ctx: Context<ExecuteJupiterArbitrage>,
        swap_data: Vec<u8>,
    ) -> Result<()> {
        instructions::jupiter_arbitrage::handler(ctx, swap_data)
    }

    /// Execute arbitrage using direct DEX calls
    pub fn execute_direct_arbitrage(
        ctx: Context<ExecuteDirectArbitrage>,
        dex_type: u8,
        amount_in: u64,
        minimum_amount_out: u64,
    ) -> Result<()> {
        instructions::direct_arbitrage::handler(ctx, dex_type, amount_in, minimum_amount_out)
    }

    /// Repay flash loan to the lending protocol
    pub fn repay_flash_loan(
        ctx: Context<RepayFlashLoan>,
        amount: u64,
        fee: u64,
    ) -> Result<()> {
        instructions::repay_flash_loan::handler(ctx, amount, fee)
    }
}

/// Flash loan receiver account validation
#[derive(Accounts)]
pub struct ReceiveFlashLoan<'info> {
    /// The flash loan receiver program (this program)
    #[account(mut)]
    pub receiver_program: Program<'info, FlashLoanReceiver>,
    
    /// Source liquidity account (from lending protocol)
    #[account(mut)]
    pub source_liquidity: Account<'info, TokenAccount>,
    
    /// Destination liquidity account (temporary holding)
    #[account(mut)]
    pub destination_liquidity: Account<'info, TokenAccount>,
    
    /// Reserve account from lending protocol
    /// CHECK: Validated by lending protocol
    #[account(mut)]
    pub reserve: UncheckedAccount<'info>,
    
    /// Lending market authority
    /// CHECK: Validated by lending protocol
    pub lending_market_authority: UncheckedAccount<'info>,
    
    /// Transfer authority for token operations
    pub transfer_authority: Signer<'info>,
    
    /// SPL Token program
    pub token_program: Program<'info, Token>,
    
    /// System program
    pub system_program: Program<'info, System>,
    
    /// Clock sysvar for timing validation
    pub clock: Sysvar<'info, Clock>,
}

/// Jupiter arbitrage execution accounts
#[derive(Accounts)]
pub struct ExecuteJupiterArbitrage<'info> {
    /// User authority executing the arbitrage
    pub user: Signer<'info>,
    
    /// Source token account
    #[account(mut)]
    pub source_token_account: Account<'info, TokenAccount>,
    
    /// Destination token account
    #[account(mut)]
    pub destination_token_account: Account<'info, TokenAccount>,
    
    /// Jupiter program
    /// CHECK: Jupiter program ID validation
    pub jupiter_program: UncheckedAccount<'info>,
    
    /// Token program
    pub token_program: Program<'info, Token>,
    
    /// System program
    pub system_program: Program<'info, System>,
}

/// Direct DEX arbitrage execution accounts
#[derive(Accounts)]
pub struct ExecuteDirectArbitrage<'info> {
    /// User authority executing the arbitrage
    pub user: Signer<'info>,
    
    /// Source token account
    #[account(mut)]
    pub source_token_account: Account<'info, TokenAccount>,
    
    /// Destination token account
    #[account(mut)]
    pub destination_token_account: Account<'info, TokenAccount>,
    
    /// DEX program (Raydium, Orca, Meteora, etc.)
    /// CHECK: DEX program ID validation in handler
    pub dex_program: UncheckedAccount<'info>,
    
    /// Pool or market account
    /// CHECK: Validated by DEX program
    #[account(mut)]
    pub pool_account: UncheckedAccount<'info>,
    
    /// Token program
    pub token_program: Program<'info, Token>,
    
    /// System program
    pub system_program: Program<'info, System>,
}

/// Flash loan repayment accounts
#[derive(Accounts)]
pub struct RepayFlashLoan<'info> {
    /// Source account for repayment
    #[account(mut)]
    pub source_account: Account<'info, TokenAccount>,
    
    /// Destination account (lending protocol)
    #[account(mut)]
    pub destination_account: Account<'info, TokenAccount>,
    
    /// Transfer authority
    pub transfer_authority: Signer<'info>,
    
    /// Token program
    pub token_program: Program<'info, Token>,
}

/// Program type for this flash loan receiver
#[derive(Clone)]
pub struct FlashLoanReceiver;

impl anchor_lang::Id for FlashLoanReceiver {
    fn id() -> Pubkey {
        ID
    }
}
