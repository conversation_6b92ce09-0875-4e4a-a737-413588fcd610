//! Jupiter aggregator arbitrage execution
//! 
//! This module handles arbitrage execution using Jupiter's swap aggregator
//! for optimal routing across multiple DEXs.

use anchor_lang::prelude::*;
use anchor_lang::solana_program::{
    instruction::{Instruction, AccountMeta},
    program::invoke,
};
use crate::{ExecuteJupiterArbitrage, error::FlashLoanError};

/// Handler for executing arbitrage using Jupiter aggregator
pub fn handler(
    ctx: Context<ExecuteJupiterArbitrage>,
    swap_data: Vec<u8>,
) -> Result<()> {
    // Validate Jupiter program ID
    let jupiter_program_id = "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4";
    require!(
        ctx.accounts.jupiter_program.key().to_string() == jupiter_program_id,
        FlashLoanError::InvalidJupiterProgram
    );
    
    // Validate swap data is not empty
    require!(!swap_data.is_empty(), FlashLoanError::InvalidSwapData);
    require!(swap_data.len() <= 1232, FlashLoanError::SwapDataTooLarge); // Max instruction size
    
    // Validate token accounts
    require!(
        ctx.accounts.source_token_account.owner == ctx.accounts.user.key(),
        FlashLoanError::InvalidTokenAccountOwner
    );
    require!(
        ctx.accounts.destination_token_account.owner == ctx.accounts.user.key(),
        FlashLoanError::InvalidTokenAccountOwner
    );
    
    // Get initial balances for profit calculation
    let initial_source_balance = ctx.accounts.source_token_account.amount;
    let initial_dest_balance = ctx.accounts.destination_token_account.amount;
    
    msg!("Executing Jupiter arbitrage");
    msg!("Initial source balance: {}", initial_source_balance);
    msg!("Initial destination balance: {}", initial_dest_balance);
    
    // Execute Jupiter swap instruction
    let jupiter_instruction = build_jupiter_swap_instruction(
        &ctx.accounts.jupiter_program.key(),
        &ctx.accounts.user.key(),
        &ctx.accounts.source_token_account.key(),
        &ctx.accounts.destination_token_account.key(),
        &swap_data,
    )?;
    
    // Invoke Jupiter swap
    invoke(
        &jupiter_instruction,
        &[
            ctx.accounts.jupiter_program.to_account_info(),
            ctx.accounts.user.to_account_info(),
            ctx.accounts.source_token_account.to_account_info(),
            ctx.accounts.destination_token_account.to_account_info(),
            ctx.accounts.token_program.to_account_info(),
            ctx.accounts.system_program.to_account_info(),
        ],
    )?;
    
    // Refresh account data to get updated balances
    ctx.accounts.source_token_account.reload()?;
    ctx.accounts.destination_token_account.reload()?;
    
    let final_source_balance = ctx.accounts.source_token_account.amount;
    let final_dest_balance = ctx.accounts.destination_token_account.amount;
    
    msg!("Final source balance: {}", final_source_balance);
    msg!("Final destination balance: {}", final_dest_balance);
    
    // Validate swap execution was successful
    require!(
        final_dest_balance > initial_dest_balance,
        FlashLoanError::SwapExecutionFailed
    );
    
    // Calculate and validate profit
    let tokens_swapped = initial_source_balance.saturating_sub(final_source_balance);
    let tokens_received = final_dest_balance.saturating_sub(initial_dest_balance);
    
    require!(tokens_swapped > 0, FlashLoanError::NoTokensSwapped);
    require!(tokens_received > 0, FlashLoanError::NoTokensReceived);
    
    // Emit arbitrage execution event
    emit!(JupiterArbitrageExecuted {
        user: ctx.accounts.user.key(),
        source_mint: ctx.accounts.source_token_account.mint,
        destination_mint: ctx.accounts.destination_token_account.mint,
        tokens_swapped,
        tokens_received,
        timestamp: Clock::get()?.unix_timestamp,
    });
    
    msg!("Jupiter arbitrage executed successfully");
    msg!("Tokens swapped: {}, Tokens received: {}", tokens_swapped, tokens_received);
    
    Ok(())
}

/// Build Jupiter swap instruction from swap data
fn build_jupiter_swap_instruction(
    jupiter_program_id: &Pubkey,
    user: &Pubkey,
    source_token_account: &Pubkey,
    destination_token_account: &Pubkey,
    swap_data: &[u8],
) -> Result<Instruction> {
    // Parse Jupiter swap instruction from swap data
    // This would typically be provided by Jupiter's Quote API response
    
    // For now, create a basic swap instruction structure
    // In production, this would parse the actual Jupiter instruction data
    let instruction = Instruction {
        program_id: *jupiter_program_id,
        accounts: vec![
            AccountMeta::new(*user, true),
            AccountMeta::new(*source_token_account, false),
            AccountMeta::new(*destination_token_account, false),
            AccountMeta::new_readonly(spl_token::id(), false),
        ],
        data: swap_data.to_vec(),
    };
    
    Ok(instruction)
}

/// Validate Jupiter swap parameters
pub fn validate_jupiter_swap_params(
    source_mint: &Pubkey,
    destination_mint: &Pubkey,
    amount: u64,
    minimum_out: u64,
) -> Result<()> {
    // Ensure different mints for arbitrage
    require!(source_mint != destination_mint, FlashLoanError::SameMintArbitrage);
    
    // Validate amounts
    require!(amount > 0, FlashLoanError::InvalidSwapAmount);
    require!(minimum_out > 0, FlashLoanError::InvalidMinimumOut);
    
    // Ensure reasonable slippage (minimum out should be at least 90% of input)
    let min_acceptable = amount.saturating_mul(90).saturating_div(100);
    require!(minimum_out >= min_acceptable, FlashLoanError::ExcessiveSlippage);
    
    Ok(())
}

/// Event emitted when Jupiter arbitrage is executed
#[event]
pub struct JupiterArbitrageExecuted {
    pub user: Pubkey,
    pub source_mint: Pubkey,
    pub destination_mint: Pubkey,
    pub tokens_swapped: u64,
    pub tokens_received: u64,
    pub timestamp: i64,
}
