//! Direct DEX arbitrage execution
//! 
//! This module handles arbitrage execution using direct calls to DEX programs
//! for maximum speed and control over execution.

use anchor_lang::prelude::*;
use anchor_lang::solana_program::{
    instruction::{Instruction, AccountMeta},
    program::invoke,
};
use crate::{ExecuteDirectArbitrage, error::FlashLoanError};

/// DEX types supported for direct arbitrage
#[derive(<PERSON>lone, Copy, Debug, PartialEq)]
pub enum DexType {
    Raydium = 0,
    Orca = 1,
    Meteora = 2,
    Serum = 3,
}

impl From<u8> for DexType {
    fn from(value: u8) -> Self {
        match value {
            0 => DexType::Raydium,
            1 => DexType::Orca,
            2 => DexType::Meteora,
            3 => DexType::Serum,
            _ => DexType::Raydium, // Default fallback
        }
    }
}

/// Handler for executing arbitrage using direct DEX calls
pub fn handler(
    ctx: Context<ExecuteDirectArbitrage>,
    dex_type: u8,
    amount_in: u64,
    minimum_amount_out: u64,
) -> Result<()> {
    let dex = DexType::from(dex_type);
    
    // Validate input parameters
    require!(amount_in > 0, FlashLoanError::InvalidSwapAmount);
    require!(minimum_amount_out > 0, FlashLoanError::InvalidMinimumOut);
    
    // Validate token accounts
    require!(
        ctx.accounts.source_token_account.owner == ctx.accounts.user.key(),
        FlashLoanError::InvalidTokenAccountOwner
    );
    require!(
        ctx.accounts.destination_token_account.owner == ctx.accounts.user.key(),
        FlashLoanError::InvalidTokenAccountOwner
    );
    
    // Validate sufficient balance
    require!(
        ctx.accounts.source_token_account.amount >= amount_in,
        FlashLoanError::InsufficientBalance
    );
    
    // Get initial balances
    let initial_source_balance = ctx.accounts.source_token_account.amount;
    let initial_dest_balance = ctx.accounts.destination_token_account.amount;
    
    msg!("Executing direct {} arbitrage", format!("{:?}", dex));
    msg!("Amount in: {}, Minimum out: {}", amount_in, minimum_amount_out);
    
    // Execute swap based on DEX type
    match dex {
        DexType::Raydium => execute_raydium_swap(&ctx, amount_in, minimum_amount_out)?,
        DexType::Orca => execute_orca_swap(&ctx, amount_in, minimum_amount_out)?,
        DexType::Meteora => execute_meteora_swap(&ctx, amount_in, minimum_amount_out)?,
        DexType::Serum => execute_serum_swap(&ctx, amount_in, minimum_amount_out)?,
    }
    
    // Refresh account data
    ctx.accounts.source_token_account.reload()?;
    ctx.accounts.destination_token_account.reload()?;
    
    let final_source_balance = ctx.accounts.source_token_account.amount;
    let final_dest_balance = ctx.accounts.destination_token_account.amount;
    
    // Validate swap execution
    let tokens_swapped = initial_source_balance.saturating_sub(final_source_balance);
    let tokens_received = final_dest_balance.saturating_sub(initial_dest_balance);
    
    require!(tokens_swapped > 0, FlashLoanError::NoTokensSwapped);
    require!(tokens_received >= minimum_amount_out, FlashLoanError::SlippageExceeded);
    
    // Emit arbitrage execution event
    emit!(DirectArbitrageExecuted {
        user: ctx.accounts.user.key(),
        dex_type,
        source_mint: ctx.accounts.source_token_account.mint,
        destination_mint: ctx.accounts.destination_token_account.mint,
        tokens_swapped,
        tokens_received,
        timestamp: Clock::get()?.unix_timestamp,
    });
    
    msg!("Direct arbitrage executed successfully");
    msg!("Tokens swapped: {}, Tokens received: {}", tokens_swapped, tokens_received);
    
    Ok(())
}

/// Execute Raydium AMM swap
fn execute_raydium_swap(
    ctx: &Context<ExecuteDirectArbitrage>,
    amount_in: u64,
    minimum_amount_out: u64,
) -> Result<()> {
    // Validate Raydium program ID
    let raydium_program_id = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8";
    require!(
        ctx.accounts.dex_program.key().to_string() == raydium_program_id,
        FlashLoanError::InvalidDexProgram
    );
    
    // Build Raydium swap instruction
    let swap_instruction = build_raydium_instruction(
        &ctx.accounts.dex_program.key(),
        &ctx.accounts.user.key(),
        &ctx.accounts.source_token_account.key(),
        &ctx.accounts.destination_token_account.key(),
        &ctx.accounts.pool_account.key(),
        amount_in,
        minimum_amount_out,
    )?;
    
    // Execute swap
    invoke(
        &swap_instruction,
        &[
            ctx.accounts.dex_program.to_account_info(),
            ctx.accounts.user.to_account_info(),
            ctx.accounts.source_token_account.to_account_info(),
            ctx.accounts.destination_token_account.to_account_info(),
            ctx.accounts.pool_account.to_account_info(),
            ctx.accounts.token_program.to_account_info(),
        ],
    )?;
    
    Ok(())
}

/// Execute Orca Whirlpool swap
fn execute_orca_swap(
    ctx: &Context<ExecuteDirectArbitrage>,
    amount_in: u64,
    minimum_amount_out: u64,
) -> Result<()> {
    // Validate Orca program ID
    let orca_program_id = "whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc";
    require!(
        ctx.accounts.dex_program.key().to_string() == orca_program_id,
        FlashLoanError::InvalidDexProgram
    );
    
    // Build Orca swap instruction
    let swap_instruction = build_orca_instruction(
        &ctx.accounts.dex_program.key(),
        &ctx.accounts.user.key(),
        &ctx.accounts.source_token_account.key(),
        &ctx.accounts.destination_token_account.key(),
        &ctx.accounts.pool_account.key(),
        amount_in,
        minimum_amount_out,
    )?;
    
    // Execute swap
    invoke(
        &swap_instruction,
        &[
            ctx.accounts.dex_program.to_account_info(),
            ctx.accounts.user.to_account_info(),
            ctx.accounts.source_token_account.to_account_info(),
            ctx.accounts.destination_token_account.to_account_info(),
            ctx.accounts.pool_account.to_account_info(),
            ctx.accounts.token_program.to_account_info(),
        ],
    )?;
    
    Ok(())
}

/// Execute Meteora DLMM swap
fn execute_meteora_swap(
    ctx: &Context<ExecuteDirectArbitrage>,
    amount_in: u64,
    minimum_amount_out: u64,
) -> Result<()> {
    // Validate Meteora program ID
    let meteora_program_id = "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo";
    require!(
        ctx.accounts.dex_program.key().to_string() == meteora_program_id,
        FlashLoanError::InvalidDexProgram
    );
    
    // Build Meteora swap instruction
    let swap_instruction = build_meteora_instruction(
        &ctx.accounts.dex_program.key(),
        &ctx.accounts.user.key(),
        &ctx.accounts.source_token_account.key(),
        &ctx.accounts.destination_token_account.key(),
        &ctx.accounts.pool_account.key(),
        amount_in,
        minimum_amount_out,
    )?;
    
    // Execute swap
    invoke(
        &swap_instruction,
        &[
            ctx.accounts.dex_program.to_account_info(),
            ctx.accounts.user.to_account_info(),
            ctx.accounts.source_token_account.to_account_info(),
            ctx.accounts.destination_token_account.to_account_info(),
            ctx.accounts.pool_account.to_account_info(),
            ctx.accounts.token_program.to_account_info(),
        ],
    )?;
    
    Ok(())
}

/// Execute Serum DEX swap
fn execute_serum_swap(
    ctx: &Context<ExecuteDirectArbitrage>,
    amount_in: u64,
    minimum_amount_out: u64,
) -> Result<()> {
    // Validate Serum program ID
    let serum_program_id = "9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin";
    require!(
        ctx.accounts.dex_program.key().to_string() == serum_program_id,
        FlashLoanError::InvalidDexProgram
    );
    
    // Build Serum swap instruction
    let swap_instruction = build_serum_instruction(
        &ctx.accounts.dex_program.key(),
        &ctx.accounts.user.key(),
        &ctx.accounts.source_token_account.key(),
        &ctx.accounts.destination_token_account.key(),
        &ctx.accounts.pool_account.key(),
        amount_in,
        minimum_amount_out,
    )?;
    
    // Execute swap
    invoke(
        &swap_instruction,
        &[
            ctx.accounts.dex_program.to_account_info(),
            ctx.accounts.user.to_account_info(),
            ctx.accounts.source_token_account.to_account_info(),
            ctx.accounts.destination_token_account.to_account_info(),
            ctx.accounts.pool_account.to_account_info(),
            ctx.accounts.token_program.to_account_info(),
        ],
    )?;
    
    Ok(())
}

/// Build Raydium swap instruction
fn build_raydium_instruction(
    program_id: &Pubkey,
    user: &Pubkey,
    source_token: &Pubkey,
    dest_token: &Pubkey,
    pool: &Pubkey,
    amount_in: u64,
    minimum_amount_out: u64,
) -> Result<Instruction> {
    // Raydium swap instruction discriminator
    let mut data = vec![0x09]; // Swap instruction
    data.extend_from_slice(&amount_in.to_le_bytes());
    data.extend_from_slice(&minimum_amount_out.to_le_bytes());
    
    Ok(Instruction {
        program_id: *program_id,
        accounts: vec![
            AccountMeta::new(*pool, false),
            AccountMeta::new(*user, true),
            AccountMeta::new(*source_token, false),
            AccountMeta::new(*dest_token, false),
            AccountMeta::new_readonly(spl_token::id(), false),
        ],
        data,
    })
}

/// Build Orca swap instruction
fn build_orca_instruction(
    program_id: &Pubkey,
    user: &Pubkey,
    source_token: &Pubkey,
    dest_token: &Pubkey,
    pool: &Pubkey,
    amount_in: u64,
    minimum_amount_out: u64,
) -> Result<Instruction> {
    // Orca swap instruction discriminator
    let mut data = vec![0xf8, 0xc6, 0x9e, 0x91, 0xe1, 0x75, 0x87, 0xc8]; // Swap discriminator
    data.extend_from_slice(&amount_in.to_le_bytes());
    data.extend_from_slice(&minimum_amount_out.to_le_bytes());
    data.extend_from_slice(&[0u8]); // a_to_b direction
    
    Ok(Instruction {
        program_id: *program_id,
        accounts: vec![
            AccountMeta::new(*pool, false),
            AccountMeta::new(*user, true),
            AccountMeta::new(*source_token, false),
            AccountMeta::new(*dest_token, false),
            AccountMeta::new_readonly(spl_token::id(), false),
        ],
        data,
    })
}

/// Build Meteora swap instruction
fn build_meteora_instruction(
    program_id: &Pubkey,
    user: &Pubkey,
    source_token: &Pubkey,
    dest_token: &Pubkey,
    pool: &Pubkey,
    amount_in: u64,
    minimum_amount_out: u64,
) -> Result<Instruction> {
    // Meteora swap instruction discriminator
    let mut data = vec![0x14, 0x1b, 0x9a, 0x38, 0x1c, 0xf0, 0x6c, 0x51]; // Swap discriminator
    data.extend_from_slice(&amount_in.to_le_bytes());
    data.extend_from_slice(&minimum_amount_out.to_le_bytes());
    
    Ok(Instruction {
        program_id: *program_id,
        accounts: vec![
            AccountMeta::new(*pool, false),
            AccountMeta::new(*user, true),
            AccountMeta::new(*source_token, false),
            AccountMeta::new(*dest_token, false),
            AccountMeta::new_readonly(spl_token::id(), false),
        ],
        data,
    })
}

/// Build Serum swap instruction
fn build_serum_instruction(
    program_id: &Pubkey,
    user: &Pubkey,
    source_token: &Pubkey,
    dest_token: &Pubkey,
    market: &Pubkey,
    amount_in: u64,
    minimum_amount_out: u64,
) -> Result<Instruction> {
    // Serum new order instruction
    let mut data = vec![0x00]; // NewOrderV3 instruction
    data.extend_from_slice(&amount_in.to_le_bytes());
    data.extend_from_slice(&minimum_amount_out.to_le_bytes());
    
    Ok(Instruction {
        program_id: *program_id,
        accounts: vec![
            AccountMeta::new(*market, false),
            AccountMeta::new(*user, true),
            AccountMeta::new(*source_token, false),
            AccountMeta::new(*dest_token, false),
            AccountMeta::new_readonly(spl_token::id(), false),
        ],
        data,
    })
}

/// Event emitted when direct arbitrage is executed
#[event]
pub struct DirectArbitrageExecuted {
    pub user: Pubkey,
    pub dex_type: u8,
    pub source_mint: Pubkey,
    pub destination_mint: Pubkey,
    pub tokens_swapped: u64,
    pub tokens_received: u64,
    pub timestamp: i64,
}
