//! SPL-compliant flash loan receiver instruction handler
//! 
//! This instruction must use discriminator 0 as per SPL flash loan specification.
//! It receives the flash loan, validates the amount, and prepares for arbitrage execution.

use anchor_lang::prelude::*;
use anchor_spl::token::{self, Token, TokenAccount, Transfer};
use crate::{ReceiveFlashLoan, error::FlashLoanError, state::FlashLoanState};

/// Handler for receiving flash loans from SPL-compliant lending protocols
pub fn handler(ctx: Context<ReceiveFlashLoan>, amount: u64) -> Result<()> {
    let clock = &ctx.accounts.clock;
    
    // Validate flash loan amount
    require!(amount > 0, FlashLoanError::InvalidLoanAmount);
    require!(amount <= u64::MAX / 2, FlashLoanError::LoanAmountTooLarge);
    
    // Validate source liquidity has sufficient balance
    let source_balance = ctx.accounts.source_liquidity.amount;
    require!(source_balance >= amount, FlashLoanError::InsufficientLiquidity);
    
    // Validate destination account is owned by this program
    require!(
        ctx.accounts.destination_liquidity.owner == ctx.accounts.receiver_program.key(),
        FlashLoanError::InvalidDestinationOwner
    );
    
    // Transfer flash loan amount from source to destination
    let transfer_ctx = CpiContext::new(
        ctx.accounts.token_program.to_account_info(),
        Transfer {
            from: ctx.accounts.source_liquidity.to_account_info(),
            to: ctx.accounts.destination_liquidity.to_account_info(),
            authority: ctx.accounts.lending_market_authority.to_account_info(),
        },
    );
    
    token::transfer(transfer_ctx, amount)?;
    
    // Initialize flash loan state for tracking
    let flash_loan_state = FlashLoanState {
        loan_amount: amount,
        borrowed_at: clock.unix_timestamp,
        source_mint: ctx.accounts.source_liquidity.mint,
        destination_account: ctx.accounts.destination_liquidity.key(),
        lending_market_authority: ctx.accounts.lending_market_authority.key(),
        is_active: true,
    };
    
    // Store flash loan state in program data account
    // This will be used for validation during repayment
    msg!("Flash loan received: {} tokens", amount);
    msg!("Flash loan state: {:?}", flash_loan_state);
    
    // Emit event for monitoring
    emit!(FlashLoanReceived {
        amount,
        mint: ctx.accounts.source_liquidity.mint,
        timestamp: clock.unix_timestamp,
    });
    
    Ok(())
}

/// Event emitted when flash loan is received
#[event]
pub struct FlashLoanReceived {
    pub amount: u64,
    pub mint: Pubkey,
    pub timestamp: i64,
}

/// Validate flash loan receiver program constraints
pub fn validate_flash_loan_constraints(
    source_liquidity: &Account<TokenAccount>,
    destination_liquidity: &Account<TokenAccount>,
    amount: u64,
) -> Result<()> {
    // Ensure same mint for source and destination
    require!(
        source_liquidity.mint == destination_liquidity.mint,
        FlashLoanError::MintMismatch
    );
    
    // Ensure destination account is empty before receiving loan
    require!(
        destination_liquidity.amount == 0,
        FlashLoanError::DestinationNotEmpty
    );
    
    // Validate amount constraints
    require!(amount > 0, FlashLoanError::InvalidLoanAmount);
    require!(amount <= source_liquidity.amount, FlashLoanError::InsufficientLiquidity);
    
    Ok(())
}

/// Calculate flash loan fee based on provider and amount
pub fn calculate_flash_loan_fee(amount: u64, fee_bps: u16) -> Result<u64> {
    let fee = (amount as u128)
        .checked_mul(fee_bps as u128)
        .ok_or(FlashLoanError::MathOverflow)?
        .checked_div(10_000)
        .ok_or(FlashLoanError::MathOverflow)?;
    
    require!(fee <= u64::MAX as u128, FlashLoanError::FeeTooLarge);
    
    Ok(fee as u64)
}

/// Validate repayment amount includes principal + fees
pub fn validate_repayment_amount(
    principal: u64,
    fee: u64,
    available_balance: u64,
) -> Result<u64> {
    let total_repayment = principal
        .checked_add(fee)
        .ok_or(FlashLoanError::MathOverflow)?;
    
    require!(
        available_balance >= total_repayment,
        FlashLoanError::InsufficientRepaymentBalance
    );
    
    Ok(total_repayment)
}
