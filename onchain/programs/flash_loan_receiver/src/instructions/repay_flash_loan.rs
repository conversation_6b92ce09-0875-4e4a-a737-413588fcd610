//! Flash loan repayment instruction handler
//! 
//! This module handles the repayment of flash loans including principal and fees
//! to ensure SPL compliance and successful transaction completion.

use anchor_lang::prelude::*;
use anchor_spl::token::{self, Token, TokenAccount, Transfer};
use crate::{RepayFlashLoan, error::FlashLoanError};

/// Handler for repaying flash loans to lending protocols
pub fn handler(
    ctx: Context<RepayFlashLoan>,
    amount: u64,
    fee: u64,
) -> Result<()> {
    // Calculate total repayment amount
    let total_repayment = amount
        .checked_add(fee)
        .ok_or(FlashLoanError::MathOverflow)?;
    
    // Validate repayment parameters
    require!(amount > 0, FlashLoanError::InvalidRepaymentAmount);
    require!(total_repayment > amount, FlashLoanError::InvalidFeeAmount);
    
    // Validate sufficient balance for repayment
    let available_balance = ctx.accounts.source_account.amount;
    require!(
        available_balance >= total_repayment,
        FlashLoanError::InsufficientRepaymentBalance
    );
    
    // Validate token mint consistency
    require!(
        ctx.accounts.source_account.mint == ctx.accounts.destination_account.mint,
        FlashLoanError::MintMismatch
    );
    
    // Validate account ownership
    require!(
        ctx.accounts.source_account.owner == ctx.accounts.transfer_authority.key(),
        FlashLoanError::InvalidAccountOwnership
    );
    
    msg!("Repaying flash loan: {} principal + {} fee = {} total", 
         amount, fee, total_repayment);
    
    // Transfer repayment amount to lending protocol
    let transfer_ctx = CpiContext::new(
        ctx.accounts.token_program.to_account_info(),
        Transfer {
            from: ctx.accounts.source_account.to_account_info(),
            to: ctx.accounts.destination_account.to_account_info(),
            authority: ctx.accounts.transfer_authority.to_account_info(),
        },
    );
    
    token::transfer(transfer_ctx, total_repayment)?;
    
    // Validate repayment was successful
    ctx.accounts.source_account.reload()?;
    ctx.accounts.destination_account.reload()?;
    
    let remaining_balance = ctx.accounts.source_account.amount;
    require!(
        remaining_balance == available_balance.saturating_sub(total_repayment),
        FlashLoanError::RepaymentValidationFailed
    );
    
    // Emit repayment event
    emit!(FlashLoanRepaid {
        principal: amount,
        fee,
        total_repayment,
        mint: ctx.accounts.source_account.mint,
        timestamp: Clock::get()?.unix_timestamp,
    });
    
    msg!("Flash loan repaid successfully");
    msg!("Remaining balance: {}", remaining_balance);
    
    Ok(())
}

/// Validate flash loan repayment constraints
pub fn validate_repayment_constraints(
    source_account: &Account<TokenAccount>,
    destination_account: &Account<TokenAccount>,
    amount: u64,
    fee: u64,
) -> Result<()> {
    // Validate amounts
    require!(amount > 0, FlashLoanError::InvalidRepaymentAmount);
    require!(fee < amount, FlashLoanError::ExcessiveFee); // Fee should be reasonable
    
    // Validate mint consistency
    require!(
        source_account.mint == destination_account.mint,
        FlashLoanError::MintMismatch
    );
    
    // Calculate total repayment
    let total_repayment = amount
        .checked_add(fee)
        .ok_or(FlashLoanError::MathOverflow)?;
    
    // Validate sufficient balance
    require!(
        source_account.amount >= total_repayment,
        FlashLoanError::InsufficientRepaymentBalance
    );
    
    Ok(())
}

/// Calculate expected fee for flash loan repayment
pub fn calculate_expected_fee(
    principal: u64,
    fee_rate_bps: u16,
) -> Result<u64> {
    let fee = (principal as u128)
        .checked_mul(fee_rate_bps as u128)
        .ok_or(FlashLoanError::MathOverflow)?
        .checked_div(10_000)
        .ok_or(FlashLoanError::MathOverflow)?;
    
    require!(fee <= u64::MAX as u128, FlashLoanError::FeeTooLarge);
    
    Ok(fee as u64)
}

/// Validate fee amount is within acceptable range
pub fn validate_fee_amount(
    principal: u64,
    fee: u64,
    max_fee_bps: u16,
) -> Result<()> {
    let max_fee = calculate_expected_fee(principal, max_fee_bps)?;
    
    require!(fee <= max_fee, FlashLoanError::ExcessiveFee);
    require!(fee > 0, FlashLoanError::InvalidFeeAmount);
    
    Ok(())
}

/// Calculate profit after flash loan repayment
pub fn calculate_profit_after_repayment(
    initial_balance: u64,
    final_balance: u64,
    principal: u64,
    fee: u64,
) -> Result<i64> {
    let total_repayment = principal
        .checked_add(fee)
        .ok_or(FlashLoanError::MathOverflow)?;
    
    let net_balance = final_balance
        .checked_sub(total_repayment)
        .ok_or(FlashLoanError::InsufficientRepaymentBalance)?;
    
    let profit = (net_balance as i64)
        .checked_sub(initial_balance as i64)
        .ok_or(FlashLoanError::MathOverflow)?;
    
    Ok(profit)
}

/// Validate minimum profit threshold is met
pub fn validate_minimum_profit(
    profit: i64,
    minimum_profit_lamports: u64,
) -> Result<()> {
    require!(
        profit >= minimum_profit_lamports as i64,
        FlashLoanError::InsufficientProfit
    );
    
    Ok(())
}

/// Event emitted when flash loan is repaid
#[event]
pub struct FlashLoanRepaid {
    pub principal: u64,
    pub fee: u64,
    pub total_repayment: u64,
    pub mint: Pubkey,
    pub timestamp: i64,
}
