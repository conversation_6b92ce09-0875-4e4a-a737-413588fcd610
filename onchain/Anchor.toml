[features]
seeds = false
skip-lint = false
[programs.localnet]
tmp = "CRQXfRGq3wTkjt7JkqhojPLiKLYLjHPGLebnfiiQB46T"
[programs.devnet]
tmp = "CRQXfRGq3wTkjt7JkqhojPLiKLYLjHPGLebnfiiQB46T"
[programs.mainnet]
tmp = "CRQXfRGq3wTkjt7JkqhojPLiKLYLjHPGLebnfiiQB46T"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "devnet"
wallet = "/home/<USER>/.config/solana/id.json"

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts"
