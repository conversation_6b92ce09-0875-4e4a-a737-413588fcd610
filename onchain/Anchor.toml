[features]
seeds = false
skip-lint = false
[programs.localnet]
flash_loan_receiver = "FLRcv1234567890123456789012345678901234567890"

[programs.devnet]
flash_loan_receiver = "FLRcv1234567890123456789012345678901234567890"

[programs.mainnet]
flash_loan_receiver = "FLRcv1234567890123456789012345678901234567890"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "localnet"
wallet = "~/.config/solana/id.json"

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts"

[test]
startup_wait = 5000
shutdown_wait = 2000
upgradeable = false

[[test.genesis]]
address = "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s"
program = "tests/fixtures/metaplex_token_metadata.so"

[test.validator]
url = "https://api.mainnet-beta.solana.com"
clone = [
    # Flash loan providers
    { address = "So1endDq2YkqhipRh3WViPa8hdiSpxWy6z3Z6tMCpAo" }, # Solend
    { address = "4MangoMjqJ2firMokCjjGgoK8d4MXcrgL7XJaL3w6fVg" }, # Mango v4
    { address = "MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA" }, # Marginfi
    { address = "KLend2g3cP87fffoy8q1mQqGKjrxjC8boSyAYavgmjD" }, # Kamino

    # DEX programs
    { address = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8" }, # Raydium AMM
    { address = "CAMMCzo5YL8w4VFF8KVHrK22GGUQpMkFr9WeqATV9Uu" }, # Raydium CLMM
    { address = "whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc" }, # Orca Whirlpools
    { address = "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo" }, # Meteora DLMM
    { address = "9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin" }, # Serum DEX

    # Jupiter aggregator
    { address = "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4" }, # Jupiter v6

    # Common tokens
    { address = "So11111111111111111111111111111111111111112" }, # Wrapped SOL
    { address = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v" }, # USDC
    { address = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB" }, # USDT
    { address = "mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So" }, # Marinade SOL
    { address = "7dHbWXmci3dT8UFYWYZweBLXgycu7Y3iL6trKn1Y7ARj" }, # Lido SOL
]

[build]
exclude = ["node_modules", "target", "tests"]
